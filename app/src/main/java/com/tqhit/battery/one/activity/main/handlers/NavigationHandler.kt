package com.tqhit.battery.one.activity.main.handlers

import android.os.Bundle
import android.util.Log
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles navigation-related logic for MainActivity.
 * Extracted from MainActivity to improve maintainability and separation of concerns.
 * 
 * Responsibilities:
 * - Dynamic navigation setup and management
 * - Manual navigation handling for non-dynamic items
 * - Fragment state restoration
 * - Navigation state synchronization
 * 
 * Following stats module architecture pattern with proper dependency injection.
 */
@Singleton
class NavigationHandler @Inject constructor(
    private val dynamicNavigationManager: DynamicNavigationManager
) {
    companion object {
        private const val TAG = "NavigationHandler"
    }

    private var isInitialFragmentSet = false
    private var currentSelectedItemId: Int = R.id.animationGridFragment // Default to Animation for 2-button nav
    private var isFragmentSetupInProgress = false

    /**
     * Sets up dynamic navigation with proper error handling and restoration support.
     * 
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to manage
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     * @param savedInstanceState Saved state for restoration (nullable)
     * @param onNavigationSelected Callback for navigation item selection
     */
    fun setupDynamicNavigation(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner,
        savedInstanceState: Bundle?,
        onNavigationSelected: (Int) -> Boolean
    ) {
        Log.d(TAG, "Setting up dynamic navigation")

        try {
            // Restore selected item ID from saved state if available
            savedInstanceState?.let { bundle ->
                val restoredItemId = bundle.getInt("selected_item_id", R.id.animationGridFragment)
                if (restoredItemId != R.id.animationGridFragment) {
                    currentSelectedItemId = restoredItemId
                    Log.d(TAG, "NAVIGATION_RESTORE: Restored selectedItemId: $currentSelectedItemId")
                }
            }

            // Check if we have a saved state to restore
            val hasRestoredState = savedInstanceState != null && currentSelectedItemId != R.id.animationGridFragment
            Log.d(TAG, "NAVIGATION_RESTORE: Has restored state: $hasRestoredState, currentSelectedItemId: $currentSelectedItemId, savedInstanceState: ${savedInstanceState != null}")

            // Initialize the dynamic navigation manager with restoration context
            dynamicNavigationManager.initialize(
                fragmentManager = fragmentManager,
                bottomNavigationView = bottomNavigationView,
                fragmentContainerId = fragmentContainerId,
                lifecycleOwner = lifecycleOwner,
                restoredSelectedItemId = if (hasRestoredState) currentSelectedItemId else null
            )

            // Set up bottom navigation listener to work with dynamic navigation
            bottomNavigationView.setOnItemSelectedListener { item ->
                val navigationStartTime = System.currentTimeMillis()
                Log.d(TAG, "NAVIGATION_PERFORMANCE: Navigation item selected: ${item.itemId} at $navigationStartTime")

                // Let the dynamic navigation manager handle the navigation
                val handled = dynamicNavigationManager.handleUserNavigation(item.itemId)
                
                if (handled) {
                    currentSelectedItemId = item.itemId
                    val navigationTime = System.currentTimeMillis() - navigationStartTime
                    Log.d(TAG, "NAVIGATION_PERFORMANCE: Dynamic navigation completed in ${navigationTime}ms")
                } else {
                    Log.w(TAG, "NAVIGATION_RESTORE: Navigation not handled by dynamic manager, falling back to manual handling")
                    // Fallback to manual fragment switching for non-dynamic items
                    val fallbackHandled = handleManualNavigation(item.itemId, fragmentManager, fragmentContainerId)
                    val fallbackTime = System.currentTimeMillis() - navigationStartTime
                    Log.d(TAG, "NAVIGATION_PERFORMANCE: Manual navigation completed in ${fallbackTime}ms")
                    return@setOnItemSelectedListener fallbackHandled
                }

                // Call the provided callback
                onNavigationSelected(item.itemId)
                handled
            }

            Log.d(TAG, "NAVIGATION_RESTORE: Dynamic navigation setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_RESTORE: Error setting up dynamic navigation", e)
            throw e // Re-throw to allow MainActivity to handle fallback
        }
    }

    /**
     * Handles manual navigation for items not managed by dynamic navigation.
     * 
     * @param itemId The menu item ID to navigate to
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param fragmentContainerId The container ID for fragment replacement
     * @return true if navigation was handled, false otherwise
     */
    fun handleManualNavigation(
        itemId: Int,
        fragmentManager: FragmentManager,
        fragmentContainerId: Int
    ): Boolean {
        val fragment = when (itemId) {
            R.id.chargeFragment -> StatsChargeFragment()
            R.id.dischargeFragment -> DischargeFragment()
            R.id.healthFragment -> HealthFragment()
            R.id.settingsFragment -> SettingsFragment()
            R.id.animationGridFragment -> AnimationGridFragment()
            R.id.othersFragment -> {
                Log.d(TAG, "Creating OthersFragment instance")
                com.tqhit.battery.one.fragment.main.others.OthersFragment()
            }
            else -> {
                Log.w(TAG, "Unknown navigation item: $itemId")
                return false
            }
        }

        Log.d(TAG, "Manual navigation to: ${fragment.javaClass.simpleName}")
        
        try {
            fragmentManager
                .beginTransaction()
                .replace(fragmentContainerId, fragment)
                .commit()

            currentSelectedItemId = itemId
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error in manual navigation", e)
            return false
        }
    }

    /**
     * Restores fragment state from saved instance state.
     * 
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to update
     * @param fragmentContainerId The container ID for fragment replacement
     */
    fun restoreFragmentState(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int
    ) {
        Log.d(TAG, "NAVIGATION_RESTORE: restoreFragmentState called - isInitialFragmentSet: $isInitialFragmentSet")

        if (isInitialFragmentSet) {
            Log.d(TAG, "NAVIGATION_RESTORE: Restoring fragment state for item: $currentSelectedItemId")

            try {
                // Try to use dynamic navigation manager first
                val currentState = dynamicNavigationManager.getCurrentState()
                if (currentState != null && dynamicNavigationManager.isInitialized()) {
                    Log.d(TAG, "NAVIGATION_RESTORE: Using dynamic navigation manager for restoration")
                    
                    // Update bottom navigation to match current state
                    bottomNavigationView.selectedItemId = currentState.activeFragmentId
                    
                    Log.d(TAG, "NAVIGATION_RESTORE: Dynamic restoration completed - activeFragment: ${currentState.activeFragmentId}")
                    return
                }
            } catch (e: Exception) {
                Log.w(TAG, "NAVIGATION_RESTORE: Error using dynamic navigation manager for restoration", e)
            }

            // Fallback to manual restoration
            Log.d(TAG, "NAVIGATION_RESTORE: Performing manual fragment state restoration")
            bottomNavigationView.selectedItemId = currentSelectedItemId
            
            val fragment = when (currentSelectedItemId) {
                R.id.chargeFragment -> StatsChargeFragment()
                R.id.dischargeFragment -> DischargeFragment()
                R.id.healthFragment -> HealthFragment()
                R.id.settingsFragment -> SettingsFragment()
                R.id.animationGridFragment -> AnimationGridFragment()
                R.id.othersFragment -> {
                    Log.d(TAG, "Creating OthersFragment instance for restoration")
                    com.tqhit.battery.one.fragment.main.others.OthersFragment()
                }
                else -> AnimationGridFragment() // Default to Animation for 2-button nav
            }

            try {
                fragmentManager
                    .beginTransaction()
                    .replace(fragmentContainerId, fragment)
                    .commit()
                
                Log.d(TAG, "NAVIGATION_RESTORE: Manual restoration completed for ${fragment.javaClass.simpleName}")
            } catch (e: Exception) {
                Log.e(TAG, "NAVIGATION_RESTORE: Error in manual fragment restoration", e)
            }
        }
    }

    /**
     * Saves navigation state to bundle.
     * 
     * @param outState The bundle to save state to
     * @param bottomNavigationView The BottomNavigationView to get current selection from
     */
    fun saveNavigationState(outState: Bundle, bottomNavigationView: BottomNavigationView) {
        val selectedItemId = bottomNavigationView.selectedItemId
        outState.putInt("selected_item_id", selectedItemId)
        Log.d(TAG, "NAVIGATION_RESTORE: Saving selectedItemId: $selectedItemId")
    }

    /**
     * Gets the current selected item ID.
     */
    fun getCurrentSelectedItemId(): Int = currentSelectedItemId

    /**
     * Sets the initial fragment setup flag.
     */
    fun setInitialFragmentSet(isSet: Boolean) {
        isInitialFragmentSet = isSet
    }

    /**
     * Checks if initial fragment is set.
     */
    fun isInitialFragmentSet(): Boolean = isInitialFragmentSet

    /**
     * Sets fragment setup in progress flag.
     */
    fun setFragmentSetupInProgress(inProgress: Boolean) {
        isFragmentSetupInProgress = inProgress
    }

    /**
     * Checks if fragment setup is in progress.
     */
    fun isFragmentSetupInProgress(): Boolean = isFragmentSetupInProgress

    /**
     * Logs navigation performance statistics.
     */
    fun logNavigationPerformanceStats() {
        try {
            val performanceStats = dynamicNavigationManager.getPerformanceStats()
            Log.d(TAG, "NAVIGATION_PERFORMANCE: $performanceStats")
        } catch (e: Exception) {
            Log.w(TAG, "Error getting navigation performance stats", e)
        }
    }
}
