package com.tqhit.battery.one.fragment.main

import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.FragmentHealthBinding
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.AndroidEntryPoint
import jakarta.inject.Inject
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

enum class HistoryType {
    PERCENTAGE,
    TEMPERATURE
}

@AndroidEntryPoint
class HealthFragment : AdLibBaseFragment<FragmentHealthBinding>() {
    override val binding by lazy { FragmentHealthBinding.inflate(layoutInflater) }

    // New modern architecture ViewModels
    private val healthViewModel: HealthViewModel by viewModels()

    // Legacy ViewModel - kept temporarily for chart functionality during migration
    private val batteryViewModel: BatteryViewModel by viewModels()

    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var chargingSessionManager: com.tqhit.battery.one.manager.charge.ChargingSessionManager
    @Inject lateinit var historyBatteryRepository: com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository
    @Inject lateinit var coreBatteryStatsProvider: com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
    private var chartUpdateJob: Job? = null
    private var selectedHour: Int = 4

    // Separate state management for each chart
    private var percentageChartTimeRange: Int = 4
    private var temperatureChartTimeRange: Int = 4
    private var lastHealthChartData: com.tqhit.battery.one.features.stats.health.data.HealthChartData? = null

    override fun setupData() {
        super.setupData()
        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Setting up health fragment data for real device testing")

        // PRODUCTION TEST: Use real device data from HistoryBatteryRepository
        observeHealthUiState()
        updateChart(HistoryType.PERCENTAGE, 4)
        updateChart(HistoryType.TEMPERATURE, 4)
        updateDailyWearChart()

        // Set up debug menu trigger for testing critical issues
        setupDebugMenuTrigger()

        // Add temporary debug button for testing session simulation
        setupTemporaryDebugButton()

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Health fragment setup complete - monitoring real battery data")
    }

    override fun setupListener() {
        super.setupListener()
        setupInfoBlockListeners()
        setupButtonListeners()
    }

    override fun setupUI() {
        super.setupUI()
        setupChartListeners()
        // Set default 4h selection for both charts
        updateChart(HistoryType.PERCENTAGE, 4)
        updateChart(HistoryType.TEMPERATURE, 4)
    }

    override fun onStart() {
        super.onStart()
        // PRODUCTION TEST: Enable auto-update to collect real battery data
        startChartAutoUpdate()
        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Auto-update enabled for real-time battery data collection")
    }

    override fun onStop() {
        super.onStop()
        chartUpdateJob?.cancel()
    }

    /**
     * PRODUCTION TEST: Auto-update mechanism for real-time battery data collection.
     * This method refreshes health data from CoreBatteryStatsService every 60 seconds.
     */
    private fun startChartAutoUpdate() {
        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Starting auto-update for real battery data collection")
        chartUpdateJob?.cancel()
        chartUpdateJob = lifecycleScope.launch {
            while (isActive) {
                try {
                    // Refresh health data from real battery sources
                    healthViewModel.refreshHealthData()
                    android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Health data refreshed from real battery sources")
                    delay(60_000) // 1 minute
                } catch (e: Exception) {
                    android.util.Log.e("HealthFragment", "PRODUCTION_TEST: Error during auto-update", e)
                    delay(60_000) // Continue trying after error
                }
            }
        }
    }

    /**
     * Updates charts using data from the new HealthViewModel.
     * This method integrates with the modern health architecture.
     */
    private fun updateChartsFromHealthData(chartData: com.tqhit.battery.one.features.stats.health.data.HealthChartData) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: updateChartsFromHealthData called")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: - Battery entries: ${chartData.batteryPercentageEntries.size}")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: - Temperature entries: ${chartData.temperatureEntries.size}")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: - Daily wear data: ${chartData.dailyWearData.size}")

        // Cache the chart data for persistence
        lastHealthChartData = chartData
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart data cached for persistence")

        // Update battery percentage chart
        updateChartFromEntries(
            chart = binding.chartPercent,
            entries = chartData.batteryPercentageEntries,
            type = HistoryType.PERCENTAGE,
            timeRangeHours = chartData.selectedTimeRangeHours
        )

        // Update temperature chart
        updateChartFromEntries(
            chart = binding.chart1,
            entries = chartData.temperatureEntries,
            type = HistoryType.TEMPERATURE,
            timeRangeHours = chartData.selectedTimeRangeHours
        )

        // Update daily wear chart
        updateDailyWearFromHealthData(chartData.dailyWearData)

        android.util.Log.d("HealthFragment", "CHART_DEBUG: Charts updated successfully")
    }

    /**
     * Updates a chart using pre-processed entries from health data.
     */
    private fun updateChartFromEntries(
        chart: com.github.mikephil.charting.charts.LineChart,
        entries: List<Entry>,
        type: HistoryType,
        timeRangeHours: Int
    ) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: updateChartFromEntries called for ${type.name}")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: - Entries count: ${entries.size}")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: - Time range: ${timeRangeHours}h")

        if (entries.isEmpty()) {
            android.util.Log.w("HealthFragment", "CHART_DEBUG: No entries available for ${type.name} chart")
            chart.clear()
            chart.setNoDataText("No chart data available.")
            chart.invalidate()
            return
        }

        android.util.Log.d("HealthFragment", "CHART_DEBUG: Setting up ${type.name} chart with ${entries.size} entries")

        val lineColor = getThemeColor(requireContext(), R.attr.colorr)
        val fillDrawable = ContextCompat.getDrawable(requireContext(), R.drawable.chart_fill)

        android.util.Log.d("HealthFragment", "CHART_DEBUG: Creating DataSet with color: $lineColor, fillDrawable: $fillDrawable")

        val dataSet = LineDataSet(entries, type.name).apply {
            lineWidth = 5f // Increased line width for better visibility
            setDrawCircles(true) // Enable circles for debugging
            circleRadius = 3f
            setDrawValues(false)
            color = lineColor
            setCircleColor(lineColor)
            mode = LineDataSet.Mode.CUBIC_BEZIER
            setDrawFilled(true)
            this.fillDrawable = fillDrawable
            fillAlpha = 120

            // Ensure the dataset is visible
            isVisible = true
            isHighlightEnabled = false
        }

        android.util.Log.d("HealthFragment", "CHART_DEBUG: DataSet configured - visible: ${dataSet.isVisible}, color: ${dataSet.color}, lineWidth: ${dataSet.lineWidth}")

        chart.data = LineData(dataSet)
        android.util.Log.d("HealthFragment", "CHART_DEBUG: ${type.name} chart data set with ${dataSet.entryCount} entries")

        // Force chart to refresh and clear any cached state
        chart.notifyDataSetChanged()
        chart.clearValues()
        chart.data = LineData(dataSet)

        // Log comprehensive chart state
        logChartState(chart, type, entries, dataSet)

        // Style the chart
        val maxTemp = if (type == HistoryType.TEMPERATURE) {
            entries.maxOfOrNull { it.y } ?: 100f
        } else null

        val roundedMax = maxTemp?.let { ((it / 5f).toInt() * 5 + 5).toFloat() }
        val yAxisValues = if (type == HistoryType.PERCENTAGE) Pair(10f, 90f) else null

        // Determine if this is sample data (x-values 0-N) or timestamp data (large timestamp values)
        val isSampleData = entries.isNotEmpty() && entries.first().x < 1000000f

        if (isSampleData) {
            // For sample data, use index-based x-axis (0 to entries.size-1)
            val minX = entries.minOfOrNull { it.x } ?: 0f
            val maxX = entries.maxOfOrNull { it.x } ?: (entries.size - 1).toFloat()
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Using sample data x-axis range: $minX to $maxX")
            styleLineChartForSampleData(chart, minX, maxX, roundedMax, yAxisValues)
        } else {
            // For timestamp data, use time-based x-axis
            val currentTime = System.currentTimeMillis()
            val startTime = currentTime - (timeRangeHours * 60 * 60 * 1000)
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Using timestamp x-axis range: $startTime to $currentTime")
            styleLineChart(chart, startTime, currentTime, roundedMax, yAxisValues)
        }

        // Update y-axis labels for temperature chart with proper temperature range
        if (type == HistoryType.TEMPERATURE) {
            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: === TEMPERATURE Y-AXIS UPDATE TRIGGERED ===")
            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: Calling updateTemperatureYAxisLabels() with ${entries.size} entries")
            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: roundedMax=$roundedMax")

            updateTemperatureYAxisLabels(entries, roundedMax)

            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: updateTemperatureYAxisLabels() call completed")
        }

        // Multiple refresh attempts to ensure chart displays
        chart.invalidate()
        chart.notifyDataSetChanged()
        chart.refreshDrawableState()

        // Post a delayed refresh to ensure UI thread processing
        chart.post {
            chart.invalidate()
            android.util.Log.d("HealthFragment", "CHART_DEBUG: ${type.name} chart post-invalidation refresh completed")
        }

        android.util.Log.d("HealthFragment", "CHART_DEBUG: ${type.name} chart invalidated with ${entries.size} entries")

        // Log final chart state after invalidation
        logChartStateAfterInvalidation(chart, type)
    }

    /**
     * Updates daily wear chart using health data.
     */
    private fun updateDailyWearFromHealthData(wearData: List<Double>) {
        val progressBars = listOf(
            binding.progbar1, binding.progbar2, binding.progbar3, binding.progbar4,
            binding.progbar5, binding.progbar6, binding.progbar7
        )

        val maxWear = wearData.maxOrNull() ?: 1.0

        // Update progress bars with scaled values (in reverse order)
        wearData.forEachIndexed { index, wear ->
            val progress = ((wear / maxWear) * 100).toInt()
            if (index < progressBars.size) {
                progressBars[6 - index].progress = progress
            }
        }

        // Update y-axis labels
        val yAxisLabels = listOf(
            binding.t9, binding.t8, binding.t7, binding.t6, binding.t5,
            binding.t4, binding.t3, binding.t2, binding.t1
        )

        val step = maxWear / 8
        yAxisLabels.forEachIndexed { index, label ->
            val value = step * index
            label.text = String.format("%.2f", value)
        }
    }

    private fun getThemeColor(context: android.content.Context, attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = context.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }

    /**
     * Observes the health UI state from the new HealthViewModel.
     * Updates all health-related UI components based on the modern architecture.
     */
    private fun observeHealthUiState() {
        lifecycleScope.launch {
            healthViewModel.uiState.collect { uiState ->
                updateHealthUI(uiState)
            }
        }
    }

    /**
     * Updates all health UI components based on the current UI state.
     */
    private fun updateHealthUI(uiState: com.tqhit.battery.one.features.stats.health.presentation.HealthUiState) {
        android.util.Log.d("HealthFragment", "UI_UPDATE: === UPDATING HEALTH UI ===")
        android.util.Log.d("HealthFragment", "UI_UPDATE: Received UI state:")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Health percentage: ${uiState.getHealthPercentage()}%")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Total sessions: ${uiState.getTotalSessions()}")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Calculation mode: ${uiState.calculationMode}")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Design capacity: ${uiState.getDesignCapacityMah()}mAh")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Effective capacity: ${uiState.getEffectiveCapacityMah()}mAh")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Has chart data: ${uiState.chartData != null}")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Loading state: ${uiState.isLoading}")
        android.util.Log.d("HealthFragment", "UI_UPDATE:   Error message: ${uiState.errorMessage ?: "None"}")

        // Update health percentage display
        val healthPercentage = uiState.getHealthPercentage()
        binding.healthFirstProgressbarCumulative.progress = healthPercentage
        binding.healthPercentDamageCumulative.text = "$healthPercentage"
        android.util.Log.d("HealthFragment", "UI_UPDATE: ✅ Health percentage UI updated: $healthPercentage%")

        // Update capacity displays
        val designCapacity = uiState.getDesignCapacityMah()
        val effectiveCapacity = uiState.getEffectiveCapacityMah()
        binding.healthFullBateryCapacity.text = designCapacity.toString()
        binding.healthCheckedBateryCapacityCumulative.text = effectiveCapacity.toString()
        android.util.Log.d("HealthFragment", "UI_UPDATE: ✅ Capacity UI updated: $designCapacity → $effectiveCapacity mAh")

        // Update session count with styled text
        val totalSessions = uiState.getTotalSessions()
        updateSessionCountDisplay(totalSessions)
        android.util.Log.d("HealthFragment", "UI_UPDATE: ✅ Session count UI updated: $totalSessions sessions")

        // Update calculation mode UI
        updateCalculationModeUI(uiState.calculationMode)
        android.util.Log.d("HealthFragment", "UI_UPDATE: ✅ Calculation mode UI updated: ${uiState.calculationMode}")

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Updating health UI - " +
            "health=${uiState.getHealthPercentage()}%, " +
            "sessions=${uiState.getTotalSessions()}, " +
            "mode=${uiState.calculationMode}, " +
            "hasChartData=${uiState.chartData != null}")

        // Update chart data if available
        uiState.chartData?.let { chartData ->
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Updating charts from health data")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: - Battery entries: ${chartData.batteryPercentageEntries.size}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: - Temperature entries: ${chartData.temperatureEntries.size}")

            // Only update if we have valid data, otherwise preserve existing charts
            if (chartData.batteryPercentageEntries.isNotEmpty() && chartData.temperatureEntries.isNotEmpty()) {
                updateChartsFromHealthData(chartData)
            } else {
                android.util.Log.w("HealthFragment", "CHART_DEBUG: Received empty chart data - preserving existing charts to prevent clearing")
                // Try to restore from cached data if available
                lastHealthChartData?.let { cachedData ->
                    android.util.Log.d("HealthFragment", "CHART_DEBUG: Restoring charts from cached data")
                    updateChartsFromHealthData(cachedData)
                }
            }
        } ?: run {
            android.util.Log.w("HealthFragment", "CHART_DEBUG: No chart data available in UI state - preserving existing charts")
            // Try to restore from cached data if available
            lastHealthChartData?.let { cachedData ->
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Restoring charts from cached data due to null UI state")
                updateChartsFromHealthData(cachedData)
            }
        }

        // Handle loading and error states
        if (uiState.isLoading) {
            // Show loading state if needed
        }

        uiState.errorMessage?.let { error ->
            // Handle error display if needed
            android.util.Log.e("HealthFragment", "Health UI error: $error")
        }
    }

    /**
     * Updates the session count display with styled highlighting.
     */
    private fun updateSessionCountDisplay(sessions: Int) {
        val context = binding.root.context
        val sessionsStr = "$sessions"
        val fullText = context.getString(R.string.calculated_for, sessions.toString())
        val start = fullText.indexOf(sessionsStr)
        val end = start + sessionsStr.length
        val spannable = SpannableString(fullText)
        val greenColor = getThemeColor(requireContext(), R.attr.colorr)
        spannable.setSpan(
            ForegroundColorSpan(greenColor),
            start,
            end,
            android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.healthCountOfSessionsCumulative.text = spannable
    }

    /**
     * Updates the calculation mode UI based on the current mode.
     */
    private fun updateCalculationModeUI(mode: HealthCalculationMode) {
        val useCumulativeWear = (mode == HealthCalculationMode.CUMULATIVE)

        binding.cumulativeBtn.background =
            ContextCompat.getDrawable(
                requireContext(),
                if (useCumulativeWear) R.drawable.grey_block_selected_line_up_left
                else R.drawable.grey_block_line_up_left
            )
        binding.singularBtn.background =
            ContextCompat.getDrawable(
                requireContext(),
                if (useCumulativeWear) R.drawable.grey_block_line_up_right
                else R.drawable.grey_block_selected_line_up_right
            )
        binding.methodText.text =
            if (useCumulativeWear) getString(R.string.cumulative_text)
            else getString(R.string.singular_text)
        binding.cumulativeSessionInfo.isInvisible = !useCumulativeWear
        binding.singularSessionInfo.isInvisible = useCumulativeWear
        binding.healthCountOfSessionsSingular.text = getString(R.string.singular_no_data)
    }

    // region UI Listeners
    private fun setupInfoBlockListeners() {
        binding.degreeWearInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.wear_rate, R.string.degree_of_wear_info)
            }
        }
    }

    private fun showInfoDialog(titleResId: Int, messageResId: Int) {
        NotificationDialog(requireContext(), getString(titleResId), getString(messageResId)).show()
    }

    private fun setupButtonListeners() {
        binding.cumulativeBtn.setOnClickListener {
            healthViewModel.switchCalculationMode(HealthCalculationMode.CUMULATIVE)
        }

        binding.singularBtn.setOnClickListener {
            healthViewModel.switchCalculationMode(HealthCalculationMode.SINGULAR)
        }

        // TEMPORARY DEBUG: Add triple-tap to singular button for session simulation
        var tapCount = 0
        var lastTapTime = 0L
        binding.singularBtn.setOnClickListener { view ->
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastTapTime < 500) { // Within 500ms
                tapCount++
            } else {
                tapCount = 1
            }
            lastTapTime = currentTime

            if (tapCount == 1) {
                // Normal singular mode switch
                healthViewModel.switchCalculationMode(HealthCalculationMode.SINGULAR)
            } else if (tapCount >= 3) {
                // Triple tap - trigger session simulation
                android.util.Log.d("HealthFragment", "DEBUG_TRIPLE_TAP: Triple tap detected on singular button - triggering session simulation")
                simulateNewChargingSession()
                tapCount = 0 // Reset
            }
        }
    }

    private fun setupChartListeners() {
        // Set default 4h button selection styling
        setDefaultButtonSelection()

        // Percentage chart buttons - only affect percentage chart
        binding.btn0.setOnClickListener {
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: === PERCENTAGE 4H BUTTON CLICKED ===")
            percentageChartTimeRange = 4
            updateButtonSelection(4, HistoryType.PERCENTAGE)

            // Log data availability before chart update
            val availableData = historyBatteryRepository.getHistoryBatteryForHours(4)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Available 4h battery data: ${availableData.size} entries")

            updatePercentageChartOnly(4)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Percentage chart 4h update completed")
        }
        binding.btn1.setOnClickListener {
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: === PERCENTAGE 8H BUTTON CLICKED ===")
            percentageChartTimeRange = 8
            updateButtonSelection(8, HistoryType.PERCENTAGE)

            // Log data availability before chart update
            val availableData = historyBatteryRepository.getHistoryBatteryForHours(8)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Available 8h battery data: ${availableData.size} entries")

            updatePercentageChartOnly(8)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Percentage chart 8h update completed")
        }
        binding.btn2.setOnClickListener {
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: === PERCENTAGE 12H BUTTON CLICKED ===")
            percentageChartTimeRange = 12
            updateButtonSelection(12, HistoryType.PERCENTAGE)

            // Log data availability before chart update
            val availableData = historyBatteryRepository.getHistoryBatteryForHours(12)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Available 12h battery data: ${availableData.size} entries")

            updatePercentageChartOnly(12)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Percentage chart 12h update completed")
        }
        binding.btn3.setOnClickListener {
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: === PERCENTAGE 24H BUTTON CLICKED ===")
            percentageChartTimeRange = 24
            updateButtonSelection(24, HistoryType.PERCENTAGE)

            // Log data availability before chart update
            val availableData = historyBatteryRepository.getHistoryBatteryForHours(24)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Available 24h battery data: ${availableData.size} entries")

            updatePercentageChartOnly(24)
            android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Percentage chart 24h update completed")
        }
        // Temperature chart buttons (Fixed IDs: btn0_t, btn1_t, etc.)
        setupTemperatureButtonListeners()
    }

    /**
     * LEGACY METHOD - Modified to prevent clearing sample data.
     * This method is called by auto-update and some legacy button handlers.
     * Instead of clearing charts when no battery history is available, it preserves existing sample data.
     */
    private fun updateChart(type: HistoryType, hours: Int) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Legacy updateChart called for ${type.name} ${hours}h - checking for data clearing prevention")

        updateDayLabels(hours, type)
        val history =
                when (type) {
                    HistoryType.PERCENTAGE -> batteryViewModel.getHistoryBatteryForHours(hours)
                    HistoryType.TEMPERATURE -> batteryViewModel.getHistoryTemperatureForHours(hours)
                }
        val chart =
                when (type) {
                    HistoryType.PERCENTAGE -> binding.chartPercent
                    HistoryType.TEMPERATURE -> binding.chart1
                }

        if (history.isEmpty()) {
            android.util.Log.w("HealthFragment", "CHART_DEBUG: Legacy updateChart found no battery history - PRESERVING existing chart data instead of clearing")

            // Instead of clearing, check if chart already has data
            if (chart.data?.dataSetCount ?: 0 > 0) {
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart already has data - preserving it")
                return
            }

            // PRODUCTION TEST: Try to get real data from health repository first
            try {
                android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Requesting real ${type.name} data from health repository")
                healthViewModel.updateChartTimeRange(hours)

                // Check if we now have real data (without delay since we're not in a coroutine)
                val currentChartData = healthViewModel.uiState.value.chartData
                if (currentChartData != null && currentChartData.isValid()) {
                    android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Real ${type.name} data obtained - ${currentChartData.batteryPercentageEntries.size} battery points, ${currentChartData.temperatureEntries.size} temp points")
                    updateChartsFromHealthData(currentChartData)
                    return
                }
            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "PRODUCTION_TEST: Error getting real data", e)
            }

            // If chart is empty, try to restore from cached data
            lastHealthChartData?.let { cachedData ->
                android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Restoring ${type.name} chart from cached data")
                val entries = when (type) {
                    HistoryType.PERCENTAGE -> cachedData.batteryPercentageEntries
                    HistoryType.TEMPERATURE -> cachedData.temperatureEntries
                }
                updateChartFromEntries(chart, entries, type, hours)
                return
            }

            // PRODUCTION TEST: Show empty chart instead of sample data for production testing
            android.util.Log.w("HealthFragment", "PRODUCTION_TEST: No real data available for ${type.name} chart - showing empty chart")
            chart.clear()
            chart.invalidate()
            return
        }

        val currentTime = System.currentTimeMillis()
        val startTime = currentTime - (hours * 60 * 60 * 1000)

        // Use milliseconds (timestamp) as x value
        val entries =
                history.map { (timestamp, value) -> Entry(timestamp.toFloat(), value.toFloat()) }

        val dataSet =
                LineDataSet(entries, type.name).apply {
                    lineWidth = 2f
                    setDrawCircles(false)
                    setDrawValues(false)
                    color = getThemeColor(requireContext(), com.tqhit.battery.one.R.attr.colorr)
                    mode = LineDataSet.Mode.CUBIC_BEZIER
                    setDrawFilled(true)
                    fillDrawable =
                            ContextCompat.getDrawable(requireContext(), R.drawable.chart_fill)
                    fillAlpha = 120
                }
        chart.data = LineData(dataSet)
        
        // Get max temperature value and round up to nearest 5
        val maxTemp = if (type == HistoryType.TEMPERATURE) {
            entries.maxOfOrNull { it.y } ?: 100f
        } else {
            null
        }
        val roundedMax = if (maxTemp != null) {
            ((maxTemp / 5f).toInt() * 5 + 5).toFloat()
        } else {
            null
        }
        
        // Set fixed y-axis values for percentage chart
        val yAxisValues = if (type == HistoryType.PERCENTAGE) {
            Pair(10f, 90f) // Fixed range from 10% to 90%
        } else {
            null
        }
        
        styleLineChart(chart, startTime, currentTime, roundedMax, yAxisValues)
        
        // Update y-axis labels for temperature chart with proper temperature range
        if (type == HistoryType.TEMPERATURE) {
            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: === TEMPERATURE Y-AXIS UPDATE TRIGGERED ===")
            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: Calling updateTemperatureYAxisLabels() with ${entries.size} entries")
            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: roundedMax=$roundedMax")

            updateTemperatureYAxisLabels(entries, roundedMax)

            android.util.Log.d("HealthFragment", "CHART_DEBUG_TEMP: updateTemperatureYAxisLabels() call completed")
        }
        
        chart.invalidate()
    }

    private fun styleLineChart(
            chart: com.github.mikephil.charting.charts.LineChart,
            startTime: Long,
            endTime: Long,
            maxY: Float? = null,
            yAxisRange: Pair<Float, Float>? = null
    ) {
        chart.setDrawGridBackground(false)
        chart.setDrawBorders(false)
        chart.description.isEnabled = false
        chart.legend.isEnabled = false

        // Disable touch gestures
        chart.setTouchEnabled(false)
        chart.isDragEnabled = false
        chart.setScaleEnabled(false)
        chart.setPinchZoom(false)

        // X Axis
        val xAxis = chart.xAxis
        xAxis.isEnabled = true
        xAxis.axisMinimum = startTime.toFloat()
        xAxis.axisMaximum = endTime.toFloat()
        xAxis.setDrawLabels(false) // Hide default labels, since you use custom ones below the chart
        xAxis.setDrawGridLines(false)
        xAxis.setDrawAxisLine(false)

        // Y Axis
        val yAxisLeft = chart.axisLeft
        yAxisLeft.isEnabled = false // Completely disable Y axis
        chart.axisRight.isEnabled = false

        // Set Y axis values based on type
        if (yAxisRange != null) {
            // For percentage chart
            chart.axisLeft.axisMinimum = yAxisRange.first
            chart.axisLeft.axisMaximum = yAxisRange.second
            chart.axisRight.axisMinimum = yAxisRange.first
            chart.axisRight.axisMaximum = yAxisRange.second
        } else if (maxY != null) {
            // For temperature chart
            chart.axisLeft.axisMinimum = 0f
            chart.axisLeft.axisMaximum = maxY
            chart.axisRight.axisMinimum = 0f
            chart.axisRight.axisMaximum = maxY
        }
    }

    /**
     * Styles a line chart for sample data with index-based x-axis.
     */
    private fun styleLineChartForSampleData(
            chart: com.github.mikephil.charting.charts.LineChart,
            minX: Float,
            maxX: Float,
            maxY: Float? = null,
            yAxisRange: Pair<Float, Float>? = null
    ) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Styling chart for sample data - x: $minX to $maxX, y: $maxY")

        chart.setDrawGridBackground(false)
        chart.setDrawBorders(false)
        chart.description.isEnabled = false
        chart.legend.isEnabled = false

        // Disable touch gestures
        chart.setTouchEnabled(false)
        chart.isDragEnabled = false
        chart.setScaleEnabled(false)
        chart.setPinchZoom(false)

        // X Axis - use index-based range for sample data
        val xAxis = chart.xAxis
        xAxis.isEnabled = true
        xAxis.axisMinimum = minX
        xAxis.axisMaximum = maxX
        xAxis.setDrawLabels(false) // Hide default labels, since you use custom ones below the chart
        xAxis.setDrawGridLines(false)
        xAxis.setDrawAxisLine(false)

        // Y Axis
        val yAxisLeft = chart.axisLeft
        yAxisLeft.isEnabled = false // Completely disable Y axis
        chart.axisRight.isEnabled = false

        // Set Y axis values based on type
        if (yAxisRange != null) {
            // For percentage chart
            chart.axisLeft.axisMinimum = yAxisRange.first
            chart.axisLeft.axisMaximum = yAxisRange.second
            chart.axisRight.axisMinimum = yAxisRange.first
            chart.axisRight.axisMaximum = yAxisRange.second
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Set percentage y-axis range: ${yAxisRange.first} to ${yAxisRange.second}")
        } else if (maxY != null) {
            // For temperature chart
            chart.axisLeft.axisMinimum = 0f
            chart.axisLeft.axisMaximum = maxY
            chart.axisRight.axisMinimum = 0f
            chart.axisRight.axisMaximum = maxY
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Set temperature y-axis range: 0 to $maxY")
        }
    }

    private fun updateDayLabels(hours: Int, type: HistoryType = HistoryType.PERCENTAGE) {
        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: === UPDATING X-AXIS TIMELINE LABELS ===")
        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Time range: ${hours}h, Chart type: ${type.name}")

        val calendar = Calendar.getInstance()
        val currentTime = calendar.timeInMillis

        // Use current time as end point and calculate start time
        val startTime = currentTime - (hours * 60 * 60 * 1000)

        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Current time: ${formatTimeForLog(currentTime)}")
        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Start time: ${formatTimeForLog(startTime)}")

        // Always use 30-minute intervals as specified in requirements
        val intervalMinutes = 30

        // Calculate the number of 30-minute intervals that fit in the time range
        val totalIntervals = (hours * 60) / intervalMinutes

        // We have 7 labels, so we need to distribute them across the time range
        val labelsToShow = 7
        val intervalsBetweenLabels = totalIntervals / (labelsToShow - 1)

        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Total 30-min intervals: $totalIntervals")
        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Intervals between labels: $intervalsBetweenLabels")

        // Update each day label with timeline calculation
        for (i in 1..7) {
            // Calculate the time for this label position
            // Label 1 = start time, Label 7 = current time
            val intervalIndex = (i - 1) * intervalsBetweenLabels
            val labelTime = startTime + (intervalIndex * intervalMinutes * 60 * 1000)

            calendar.timeInMillis = labelTime
            val hour = calendar.get(Calendar.HOUR_OF_DAY)
            val minute = calendar.get(Calendar.MINUTE)

            // Format time in HH:MM format as specified in requirements
            val timeText = String.format("%02d:%02d", hour, minute)

            android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Label $i -> $timeText (${formatTimeForLog(labelTime)})")

            when (type) {
                HistoryType.PERCENTAGE -> {
                    when (i) {
                        1 -> binding.day7Percent.text = timeText
                        2 -> binding.day6Percent.text = timeText
                        3 -> binding.day5Percent.text = timeText
                        4 -> binding.day4Percent.text = timeText
                        5 -> binding.day3Percent.text = timeText
                        6 -> binding.day2Percent.text = timeText
                        7 -> binding.day1Percent.text = timeText
                    }
                }
                HistoryType.TEMPERATURE -> {
                    when (i) {
                        1 -> binding.day7Temp.text = timeText
                        2 -> binding.day6Temp.text = timeText
                        3 -> binding.day5Temp.text = timeText
                        4 -> binding.day4Temp.text = timeText
                        5 -> binding.day3Temp.text = timeText
                        6 -> binding.day2Temp.text = timeText
                        7 -> binding.day1Temp.text = timeText
                    }
                }
            }
        }

        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: X-axis timeline labels updated successfully")
    }

    /**
     * Helper method to format timestamp for logging purposes
     */
    private fun formatTimeForLog(timestamp: Long): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        val day = calendar.get(Calendar.DAY_OF_MONTH)
        val month = calendar.get(Calendar.MONTH) + 1
        return String.format("%02d/%02d %02d:%02d", day, month, hour, minute)
    }

    private fun updateDailyWearChart() {
        val wearData = batteryViewModel.getDailyWearData(7)
        val progressBars =
                listOf(
                        binding.progbar1,
                        binding.progbar2,
                        binding.progbar3,
                        binding.progbar4,
                        binding.progbar5,
                        binding.progbar6,
                        binding.progbar7
                )

        // Find the maximum wear value to scale the progress bars
        val maxWear = wearData.maxOrNull() ?: 1.0

        // Update progress bars with scaled values (in reverse order)
        wearData.forEachIndexed { index, wear ->
            val progress = ((wear / maxWear) * 100).toInt()
            progressBars[6 - index].progress = progress
        }

        // Update day labels (in reverse order)
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("dd.MM", Locale.getDefault())

        for (i in 0..6) {
            calendar.timeInMillis = System.currentTimeMillis() - ((6 - i) * 24 * 60 * 60 * 1000)
            val dayLabel =
                    when (i) {
                        0 -> binding.day7
                        1 -> binding.day6
                        2 -> binding.day5
                        3 -> binding.day4
                        4 -> binding.day3
                        5 -> binding.day2
                        6 -> binding.day1
                        else -> null
                    }
            dayLabel?.text = dateFormat.format(calendar.time)
        }

        // Add y-axis labels (from top to bottom)
        val yAxisLabels =
                listOf(
                        binding.t9,
                        binding.t8,
                        binding.t7,
                        binding.t6,
                        binding.t5,
                        binding.t4,
                        binding.t3,
                        binding.t2,
                        binding.t1
                )

        // Calculate step size for y-axis labels
        val step = maxWear / 8 // Divide by 8 to get 9 points (0 to 8)
        yAxisLabels.forEachIndexed { index, label ->
            val value = step * index
            label.text = String.format("%.2f", value)
        }
    }

    /**
     * Sets the default 4h button selection for both chart types.
     */
    private fun setDefaultButtonSelection() {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Setting default 4h button selection")
        updateButtonSelection(4, HistoryType.PERCENTAGE)
        updateButtonSelection(4, HistoryType.TEMPERATURE)
    }

    /**
     * Updates button selection styling for the specified time range and chart type.
     */
    private fun updateButtonSelection(hours: Int, chartType: HistoryType) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Updating button selection for ${hours}h ${chartType.name}")

        when (chartType) {
            HistoryType.PERCENTAGE -> {
                // Reset all percentage buttons to default
                binding.btn0.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)
                binding.btn1.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)
                binding.btn2.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)
                binding.btn3.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)

                // Set selected button to green
                when (hours) {
                    4 -> binding.btn0.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
                    8 -> binding.btn1.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
                    12 -> binding.btn2.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
                    24 -> binding.btn3.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
                }
            }
            HistoryType.TEMPERATURE -> {
                // Use fallback method for temperature buttons due to binding issues
                updateTemperatureButtonsWithFallback(hours)
            }
        }
    }

    /**
     * Sets up temperature button click listeners using findViewById to handle binding issues.
     */
    private fun setupTemperatureButtonListeners() {
        try {
            val btn0T = requireView().findViewById<TextView>(R.id.btn0_t)
            val btn1T = requireView().findViewById<TextView>(R.id.btn1_t)
            val btn2T = requireView().findViewById<TextView>(R.id.btn2_t)
            val btn3T = requireView().findViewById<TextView>(R.id.btn3_t)

            btn0T?.setOnClickListener {
                android.util.Log.d("HealthFragment", "BUTTON_DEBUG: === TEMPERATURE 4H BUTTON CLICKED ===")
                temperatureChartTimeRange = 4
                updateButtonSelection(4, HistoryType.TEMPERATURE)

                // Log data availability before chart update
                val availableData = historyBatteryRepository.getHistoryTemperatureForHours(4)
                android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Available 4h temperature data: ${availableData.size} entries")

                updateTemperatureChartOnly(4)
                android.util.Log.d("HealthFragment", "BUTTON_DEBUG: Temperature chart 4h update completed")
            }

            btn1T?.setOnClickListener {
                temperatureChartTimeRange = 8
                updateButtonSelection(8, HistoryType.TEMPERATURE)
                updateTemperatureChartOnly(8)
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Temperature chart button 8h clicked - only affecting temperature chart")
            }

            btn2T?.setOnClickListener {
                temperatureChartTimeRange = 12
                updateButtonSelection(12, HistoryType.TEMPERATURE)
                updateTemperatureChartOnly(12)
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Temperature chart button 12h clicked - only affecting temperature chart")
            }

            btn3T?.setOnClickListener {
                temperatureChartTimeRange = 24
                updateButtonSelection(24, HistoryType.TEMPERATURE)
                updateTemperatureChartOnly(24)
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Temperature chart button 24h clicked - only affecting temperature chart")
            }

            android.util.Log.d("HealthFragment", "CHART_DEBUG: Temperature button listeners set up successfully")
        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "CHART_DEBUG: Failed to set up temperature button listeners", e)
        }
    }

    /**
     * Fallback method for temperature button selection with different naming convention.
     */
    private fun updateTemperatureButtonsWithFallback(hours: Int) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Using fallback temperature button selection")
        try {
            // Try to access temperature buttons using findViewById as fallback
            val btn0T = requireView().findViewById<TextView>(R.id.btn0_t)
            val btn1T = requireView().findViewById<TextView>(R.id.btn1_t)
            val btn2T = requireView().findViewById<TextView>(R.id.btn2_t)
            val btn3T = requireView().findViewById<TextView>(R.id.btn3_t)

            // Reset all to default
            btn0T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)
            btn1T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)
            btn2T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)
            btn3T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.grey_block)

            // Set selected button to green
            when (hours) {
                4 -> btn0T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
                8 -> btn1T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
                12 -> btn2T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
                24 -> btn3T?.background = ContextCompat.getDrawable(requireContext(), R.drawable.green_button)
            }

            android.util.Log.d("HealthFragment", "CHART_DEBUG: Fallback temperature button selection successful")
        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "CHART_DEBUG: Fallback temperature button selection failed", e)
        }
    }

    /**
     * Forces sample data generation specifically for 4h range on fragment initialization.
     */
    private fun forceSampleDataFor4hRange() {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Forcing sample data generation for 4h range")
        // Explicitly request 4h chart data to ensure sample data is generated
        healthViewModel.updateChartTimeRange(4)
    }

    /**
     * Forces a sample data refresh for charts.
     */
    private fun forceSampleDataRefresh() {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Forcing sample data refresh")
        healthViewModel.refreshHealthData()
    }

    /**
     * Updates only the percentage chart with the specified time range using real data.
     * This prevents cross-chart interference and uses authentic device data.
     */
    private fun updatePercentageChartOnly(timeRangeHours: Int) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: === UPDATING PERCENTAGE CHART FOR ${timeRangeHours}h ===")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Using HistoryBatteryRepository (NEW) instead of legacy BatteryViewModel")

        // Update X-axis timeline labels for percentage chart
        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Updating percentage chart X-axis labels for ${timeRangeHours}h")
        updateDayLabels(timeRangeHours, HistoryType.PERCENTAGE)

        // FIXED: Use HistoryBatteryRepository directly instead of legacy BatteryViewModel
        val batteryHistory = historyBatteryRepository.getHistoryBatteryForHours(timeRangeHours)

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Retrieved ${batteryHistory.size} battery entries from HistoryBatteryRepository for ${timeRangeHours}h")

        if (batteryHistory.isEmpty()) {
            android.util.Log.w("HealthFragment", "PRODUCTION_TEST: No real battery history available for ${timeRangeHours}h - this indicates data collection issue")

            // Log diagnostic information
            val totalBatteryEntries = historyBatteryRepository.getBatteryHistoryCount()
            android.util.Log.w("HealthFragment", "PRODUCTION_TEST: Total battery entries in HistoryBatteryRepository: $totalBatteryEntries")

            // Check if CoreBatteryStatsService is running
            android.util.Log.w("HealthFragment", "PRODUCTION_TEST: Checking CoreBatteryStatsService status...")
            val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
            if (currentStatus != null) {
                android.util.Log.d("HealthFragment", "PRODUCTION_TEST: CoreBatteryStatsService is active - current: ${currentStatus.percentage}%, temp: ${currentStatus.temperatureCelsius}°C")
            } else {
                android.util.Log.e("HealthFragment", "PRODUCTION_TEST: CoreBatteryStatsService is NOT providing data!")
            }

            binding.chartPercent.clear()
            binding.chartPercent.setNoDataText("No battery data available for ${timeRangeHours}h period.\nData collection may still be in progress.")
            binding.chartPercent.invalidate()
            return
        }

        // Convert real history entries to chart entries with proper timestamp mapping
        val entries = convertHistoryToChartEntries(batteryHistory)

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Using ${entries.size} real battery data points for percentage chart")
        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Time range: ${batteryHistory.first().timestamp} to ${batteryHistory.last().timestamp}")

        // Update only the percentage chart with real data
        updateChartFromEntries(
            chart = binding.chartPercent,
            entries = entries,
            type = HistoryType.PERCENTAGE,
            timeRangeHours = timeRangeHours
        )

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Percentage chart updated with REAL device data from HistoryBatteryRepository")
    }

    /**
     * Updates only the temperature chart with the specified time range using real data.
     * This prevents cross-chart interference and uses authentic device data.
     */
    private fun updateTemperatureChartOnly(timeRangeHours: Int) {
        android.util.Log.d("HealthFragment", "CHART_DEBUG: === UPDATING TEMPERATURE CHART FOR ${timeRangeHours}h ===")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Using HistoryBatteryRepository (NEW) instead of legacy BatteryViewModel")

        // Update X-axis timeline labels for temperature chart
        android.util.Log.d("HealthFragment", "TIMELINE_DEBUG: Updating temperature chart X-axis labels for ${timeRangeHours}h")
        updateDayLabels(timeRangeHours, HistoryType.TEMPERATURE)

        // FIXED: Use HistoryBatteryRepository directly instead of legacy BatteryViewModel
        val temperatureHistory = historyBatteryRepository.getHistoryTemperatureForHours(timeRangeHours)

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Retrieved ${temperatureHistory.size} temperature entries from HistoryBatteryRepository for ${timeRangeHours}h")

        if (temperatureHistory.isEmpty()) {
            android.util.Log.w("HealthFragment", "PRODUCTION_TEST: No real temperature history available for ${timeRangeHours}h - this indicates data collection issue")

            // Log diagnostic information
            val totalTempEntries = historyBatteryRepository.getTemperatureHistoryCount()
            android.util.Log.w("HealthFragment", "PRODUCTION_TEST: Total temperature entries in HistoryBatteryRepository: $totalTempEntries")

            binding.chart1.clear()
            binding.chart1.setNoDataText("No temperature data available for ${timeRangeHours}h period.\nData collection may still be in progress.")
            binding.chart1.invalidate()
            return
        }

        // Convert real history entries to chart entries with proper timestamp mapping
        val entries = convertHistoryToChartEntries(temperatureHistory)

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Using ${entries.size} real temperature data points for temperature chart")
        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Time range: ${temperatureHistory.first().timestamp} to ${temperatureHistory.last().timestamp}")

        // Update only the temperature chart with real data
        updateChartFromEntries(
            chart = binding.chart1,
            entries = entries,
            type = HistoryType.TEMPERATURE,
            timeRangeHours = timeRangeHours
        )

        android.util.Log.d("HealthFragment", "PRODUCTION_TEST: Temperature chart updated with REAL device data from HistoryBatteryRepository")
    }

    /**
     * Converts HistoryEntry objects to chart Entry objects with proper timestamp mapping.
     * This ensures X-axis represents actual time progression instead of sample indices.
     */
    private fun <T : Number> convertHistoryToChartEntries(historyEntries: List<com.tqhit.battery.one.manager.graph.HistoryEntry<T>>): List<Entry> {
        if (historyEntries.isEmpty()) {
            return emptyList()
        }

        // Sort by timestamp to ensure proper chronological order
        val sortedEntries = historyEntries.sortedBy { it.timestamp }

        // Use actual timestamps as X values for proper time-based charting
        val chartEntries = sortedEntries.map { historyEntry ->
            Entry(historyEntry.timestamp.toFloat(), historyEntry.value.toFloat())
        }

        android.util.Log.d("HealthFragment", "CHART_DEBUG: Converted ${historyEntries.size} history entries to chart entries")
        android.util.Log.d("HealthFragment", "CHART_DEBUG: Time range: ${sortedEntries.first().timestamp} to ${sortedEntries.last().timestamp}")

        return chartEntries
    }

    /**
     * Updates temperature Y-axis labels with proper temperature range calculations.
     * FIXED: Corrected Y-axis ordering - t1Temp is TOP label, t9Temp is BOTTOM label.
     */
    private fun updateTemperatureYAxisLabels(entries: List<Entry>, roundedMax: Float?) {
        try {
            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_ENTRY: === updateTemperatureYAxisLabels CALLED ===")
            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_ENTRY: Parameters - entries.size=${entries.size}, roundedMax=$roundedMax")
            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_ENTRY: Thread: ${Thread.currentThread().name}")
            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_ENTRY: Stack trace:", Exception("Stack trace"))

            // FIXED: Correct Y-axis ordering based on layout structure
            // In the layout, t1_temp is at the TOP and t9_temp is at the BOTTOM
            val yAxisLabels = listOf(
                binding.t1Temp, // TOP label (highest temperature)
                binding.t2Temp,
                binding.t3Temp,
                binding.t4Temp,
                binding.t5Temp,
                binding.t6Temp,
                binding.t7Temp,
                binding.t8Temp,
                binding.t9Temp  // BOTTOM label (lowest temperature)
            )

            // Verify that all TextView references are valid and log current values
            yAxisLabels.forEachIndexed { index, textView ->
                if (textView == null) {
                    android.util.Log.e("HealthFragment", "TEMP_Y_AXIS_ERROR: TextView at index $index is null!")
                    return
                } else {
                    val currentText = textView.text.toString()
                    android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_BEFORE: Label ${index + 1} (${textView.id}) current text: '$currentText'")
                }
            }

            if (entries.isEmpty()) {
                // Clear labels when no data
                android.util.Log.w("HealthFragment", "TEMP_Y_AXIS_EMPTY: No entries - clearing all temperature labels")
                yAxisLabels.forEachIndexed { index, textView ->
                    val beforeText = textView.text.toString()
                    textView.text = ""
                    val afterText = textView.text.toString()
                    android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_CLEAR: Label ${index + 1} (${textView.id}): '$beforeText' → '$afterText'")
                }
                android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_EXIT: Cleared temperature Y-axis labels (no data)")
                return
            }

            // Log all entry values for debugging
            entries.forEachIndexed { index, entry ->
                android.util.Log.v("HealthFragment", "TEMP_Y_AXIS_DATA: Entry $index: x=${entry.x}, y=${entry.y}°C")
            }

            // Calculate actual temperature range from real data
            val minTemp = entries.minOfOrNull { it.y } ?: 20f
            val maxTemp = entries.maxOfOrNull { it.y } ?: 40f

            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_CALC: Raw temperature range: ${minTemp}°C to ${maxTemp}°C")

            // Add some padding to the range for better visualization
            val tempRange = maxTemp - minTemp
            val padding = if (tempRange > 0) tempRange * 0.1f else 5f
            val adjustedMin = (minTemp - padding).coerceAtLeast(0f)
            val adjustedMax = maxTemp + padding

            // Calculate step size for 9 labels (8 intervals)
            val step = (adjustedMax - adjustedMin) / 8f

            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_CALC: Adjusted range: ${adjustedMin}°C to ${adjustedMax}°C, step: ${step}°C")

            // FIXED: Update y-axis labels from TOP (t1Temp = highest) to BOTTOM (t9Temp = lowest)
            // The layout has t1Temp at the top and t9Temp at the bottom
            yAxisLabels.forEachIndexed { index, label ->
                // FIXED: Reverse the temperature calculation
                // index 0 (t1Temp) = highest temperature (adjustedMax)
                // index 8 (t9Temp) = lowest temperature (adjustedMin)
                val temperature = adjustedMax - (step * index)
                val formattedTemp = String.format("%.0f", temperature)

                // Log before update
                val beforeText = label.text.toString()
                android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_UPDATE: Label ${index + 1} (${label.id}) BEFORE: '$beforeText'")
                android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_FIX: Label ${index + 1} position=${if(index == 0) "TOP" else if(index == 8) "BOTTOM" else "MIDDLE"}, temp=${temperature}°C")

                // Update the label text
                label.text = formattedTemp

                // Log after update
                val afterText = label.text.toString()
                android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_UPDATE: Label ${index + 1} (${label.id}) AFTER: '$afterText' (expected: '$formattedTemp')")

                // Verify the update worked
                if (afterText != formattedTemp) {
                    android.util.Log.e("HealthFragment", "TEMP_Y_AXIS_ERROR: Label ${index + 1} update FAILED! Expected '$formattedTemp', got '$afterText'")
                }

                // Force UI update
                label.invalidate()
                label.requestLayout()

                android.util.Log.v("HealthFragment", "TEMP_Y_AXIS_UPDATE: Label ${index + 1} invalidated and layout requested")
            }

            // Force parent layout update
            binding.root.invalidate()
            binding.root.requestLayout()

            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_SUCCESS: Temperature Y-axis labels updated successfully - forced UI refresh")

            // Final verification - check all labels again
            yAxisLabels.forEachIndexed { index, textView ->
                val finalText = textView.text.toString()
                android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_FINAL: Label ${index + 1} (${textView.id}) final text: '$finalText'")
            }

            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_EXIT: === updateTemperatureYAxisLabels COMPLETED ===")

        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "TEMP_Y_AXIS_EXCEPTION: Failed to update temperature Y-axis labels", e)
        }
    }

    /**
     * Clears all cached session data for testing purposes.
     * This resolves the stuck cumulative sessions counter issue.
     * Should be called when testing real session tracking functionality.
     */
    fun clearCachedSessionDataForTesting() {
        android.util.Log.d("HealthFragment", "SESSION_RESET_ENTRY: === clearCachedSessionDataForTesting CALLED ===")
        android.util.Log.d("HealthFragment", "SESSION_RESET_ENTRY: Thread: ${Thread.currentThread().name}")
        android.util.Log.d("HealthFragment", "SESSION_RESET_ENTRY: Stack trace:", Exception("Stack trace"))

        lifecycleScope.launch {
            try {
                android.util.Log.d("HealthFragment", "SESSION_RESET_START: Starting session clearing process")

                // Get session counts before clearing
                val chargingSessionsBefore = try {
                    // Try to get session count if possible
                    android.util.Log.d("HealthFragment", "SESSION_RESET_BEFORE: Attempting to get charging session count")
                    "unknown" // We'll get this from the ViewModel logs
                } catch (e: Exception) {
                    android.util.Log.w("HealthFragment", "SESSION_RESET_BEFORE: Could not get charging session count", e)
                    "error"
                }

                android.util.Log.d("HealthFragment", "SESSION_RESET_BEFORE: Charging sessions before clearing: $chargingSessionsBefore")

                // Clear charging sessions through the battery repository
                android.util.Log.d("HealthFragment", "SESSION_RESET_CLEAR: Calling batteryViewModel.clearChargingSessions()")
                batteryViewModel.clearChargingSessions()

                android.util.Log.d("HealthFragment", "SESSION_RESET_CLEAR: Charging sessions cleared")

                // Also clear discharge sessions to ensure complete reset
                android.util.Log.d("HealthFragment", "SESSION_RESET_CLEAR: Calling batteryViewModel.clearDischargeSessions()")
                batteryViewModel.clearDischargeSessions()

                android.util.Log.d("HealthFragment", "SESSION_RESET_CLEAR: Discharge sessions cleared")

                // Wait a moment for clearing to complete
                kotlinx.coroutines.delay(1000)

                android.util.Log.d("HealthFragment", "SESSION_RESET_REFRESH: Calling setupData() to refresh health fragment")

                // Refresh the health data to reflect the cleared sessions
                setupData()

                android.util.Log.d("HealthFragment", "SESSION_RESET_SUCCESS: Session data cleared - health fragment refreshed")

                // Wait another moment and check if sessions are still cleared
                kotlinx.coroutines.delay(2000)
                android.util.Log.d("HealthFragment", "SESSION_RESET_VERIFY: Verification delay completed - check logs for session regeneration")

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "SESSION_RESET_EXCEPTION: Failed to clear session data", e)
            }
        }

        android.util.Log.d("HealthFragment", "SESSION_RESET_EXIT: === clearCachedSessionDataForTesting COMPLETED ===")
    }

    /**
     * Manual test method to trigger temperature Y-axis label updates.
     * This can be called from ADB or debugging to test the temperature labeling fix.
     */
    fun testTemperatureYAxisLabels() {
        android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_TEST: Manual temperature Y-axis test triggered")

        try {
            // Create test temperature entries
            val testEntries = listOf(
                Entry(1f, 25.5f),  // 25.5°C
                Entry(2f, 30.8f),  // 30.8°C
                Entry(3f, 35.2f),  // 35.2°C
                Entry(4f, 28.1f)   // 28.1°C
            )

            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_TEST: Testing with ${testEntries.size} temperature entries")

            // Call the temperature Y-axis update method directly
            updateTemperatureYAxisLabels(testEntries, null)

            android.util.Log.d("HealthFragment", "TEMP_Y_AXIS_TEST: Temperature Y-axis test completed")

        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "TEMP_Y_AXIS_TEST: Failed to test temperature Y-axis labels", e)
        }
    }

    /**
     * Comprehensive test method that can be triggered via ADB for debugging both issues.
     * This tests both session clearing and temperature Y-axis labeling.
     */
    fun runComprehensiveHealthFragmentTest() {
        android.util.Log.d("HealthFragment", "COMPREHENSIVE_TEST: Starting comprehensive Health fragment test")

        lifecycleScope.launch {
            try {
                // Test 1: Clear session data
                android.util.Log.d("HealthFragment", "COMPREHENSIVE_TEST: Step 1 - Testing session clearing")
                clearCachedSessionDataForTesting()

                // Wait for session clearing to complete
                kotlinx.coroutines.delay(2000)

                // Test 2: Test temperature Y-axis labels
                android.util.Log.d("HealthFragment", "COMPREHENSIVE_TEST: Step 2 - Testing temperature Y-axis labels")
                testTemperatureYAxisLabels()

                // Test 3: Force chart updates with real data
                android.util.Log.d("HealthFragment", "COMPREHENSIVE_TEST: Step 3 - Testing chart updates")
                updatePercentageChartOnly(4)
                updateTemperatureChartOnly(4)

                android.util.Log.d("HealthFragment", "COMPREHENSIVE_TEST: Comprehensive test completed successfully")

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "COMPREHENSIVE_TEST: Comprehensive test failed", e)
            }
        }
    }

    /**
     * Sets up a debug menu trigger that can be activated via long press on the health percentage.
     * This allows easy testing of critical issues without ADB commands.
     */
    private fun setupDebugMenuTrigger() {
        try {
            android.util.Log.d("HealthFragment", "DEBUG_MENU: Setting up debug menu trigger")

            // Set up long press listener on health percentage display
            binding.healthPercentDamageCumulative.setOnLongClickListener {
                android.util.Log.d("HealthFragment", "DEBUG_MENU: Debug menu triggered via long press")
                showDebugMenu()
                true
            }

            // Also set up long press on the health progress bar as alternative trigger
            binding.healthFirstProgressbarCumulative.setOnLongClickListener {
                android.util.Log.d("HealthFragment", "DEBUG_MENU: Debug menu triggered via progress bar long press")
                showDebugMenu()
                true
            }

            android.util.Log.d("HealthFragment", "DEBUG_MENU: Debug menu triggers set up successfully")

        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "DEBUG_MENU: Failed to set up debug menu trigger", e)
        }
    }

    /**
     * Shows a debug menu with options to test critical issues.
     */
    private fun showDebugMenu() {
        try {
            android.util.Log.d("HealthFragment", "DEBUG_MENU: Showing debug menu")

            val options = arrayOf(
                "🔋 Simulate Battery Percentage Changes",
                "Simulate New Charging Session",
                "Clear All Sessions (Test Real Data)",
                "Generate Sample Sessions",
                "Log Current Data Sources",
                "Test Chart Data Sources",
                "Test Session Clearing",
                "Test Temperature Y-Axis Labels",
                "Run Comprehensive Test",
                "Force Chart Updates",
                "📊 Generate Sample Chart Data (24h)",
                "Cancel"
            )

            val builder = android.app.AlertDialog.Builder(requireContext())
            builder.setTitle("Health Fragment Debug Menu")
            builder.setItems(options) { dialog, which ->
                when (which) {
                    0 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected simulate battery percentage changes")
                        simulateBatteryPercentageChanges()
                    }
                    1 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected simulate new charging session")
                        simulateNewChargingSession()
                    }
                    2 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected clear all sessions")
                        clearAllSessionsForTesting()
                    }
                    3 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected generate sample sessions")
                        generateSampleSessions()
                    }
                    4 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected log current data sources")
                        logCurrentDataSources()
                    }
                    5 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected test chart data sources")
                        testChartDataSources()
                    }
                    6 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected session clearing test")
                        clearCachedSessionDataForTesting()
                    }
                    7 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected temperature Y-axis test")
                        testTemperatureYAxisLabels()
                    }
                    8 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected comprehensive test")
                        runComprehensiveHealthFragmentTest()
                    }
                    9 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected force chart updates")
                        updatePercentageChartOnly(4)
                        updateTemperatureChartOnly(4)
                    }
                    10 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected generate sample data for testing")
                        generateSampleDataForTesting()
                    }
                    11 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User selected generate sample chart data")
                        generateSampleDataForTesting()
                    }
                    12 -> {
                        android.util.Log.d("HealthFragment", "DEBUG_MENU: User cancelled debug menu")
                        dialog.dismiss()
                    }
                }
            }
            builder.show()

        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "DEBUG_MENU: Failed to show debug menu", e)
        }
    }

    /**
     * Generates sample data for testing time range buttons when insufficient real data exists.
     */
    private fun generateSampleDataForTesting() {
        android.util.Log.d("HealthFragment", "SAMPLE_DATA: === GENERATING SAMPLE DATA FOR TESTING ===")

        lifecycleScope.launch {
            try {
                // Generate 24 hours of sample data
                historyBatteryRepository.generateSampleDataForTesting(24)

                // Wait for data to be saved
                kotlinx.coroutines.delay(500)

                // Force chart updates to show the new data
                updatePercentageChartOnly(4)
                updateTemperatureChartOnly(4)

                // Log the results
                val batteryCount = historyBatteryRepository.getBatteryHistoryCount()
                val tempCount = historyBatteryRepository.getTemperatureHistoryCount()

                android.util.Log.d("HealthFragment", "SAMPLE_DATA: ✅ Generated sample data - Battery: $batteryCount, Temperature: $tempCount")

                android.widget.Toast.makeText(requireContext(),
                    "📊 Sample data generated: $batteryCount battery + $tempCount temperature entries",
                    android.widget.Toast.LENGTH_LONG).show()

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "SAMPLE_DATA: Error generating sample data", e)
                android.widget.Toast.makeText(requireContext(),
                    "Error generating sample data: ${e.message}",
                    android.widget.Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * Logs current data sources for debugging the "No battery data" issue.
     */
    private fun logCurrentDataSources() {
        android.util.Log.d("HealthFragment", "DATA_SOURCES: === COMPREHENSIVE DATA SOURCE ANALYSIS ===")

        try {
            // Check HistoryBatteryRepository (NEW - should have real data)
            val batteryCount = historyBatteryRepository.getBatteryHistoryCount()
            val tempCount = historyBatteryRepository.getTemperatureHistoryCount()
            android.util.Log.d("HealthFragment", "DATA_SOURCES: HistoryBatteryRepository (NEW) - Battery: $batteryCount, Temperature: $tempCount")

            // Test different time ranges
            val timeRanges = listOf(4, 8, 12, 24)
            for (hours in timeRanges) {
                val batteryEntries = historyBatteryRepository.getHistoryBatteryForHours(hours)
                val tempEntries = historyBatteryRepository.getHistoryTemperatureForHours(hours)
                android.util.Log.d("HealthFragment", "DATA_SOURCES: HistoryBatteryRepository ${hours}h - Battery: ${batteryEntries.size}, Temperature: ${tempEntries.size}")
            }

            // Check legacy BatteryViewModel (OLD - may be empty)
            for (hours in timeRanges) {
                val legacyBattery = batteryViewModel.getHistoryBatteryForHours(hours)
                val legacyTemp = batteryViewModel.getHistoryTemperatureForHours(hours)
                android.util.Log.d("HealthFragment", "DATA_SOURCES: BatteryViewModel (LEGACY) ${hours}h - Battery: ${legacyBattery.size}, Temperature: ${legacyTemp.size}")
            }

            // Check CoreBatteryStatsService status
            val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
            if (currentStatus != null) {
                android.util.Log.d("HealthFragment", "DATA_SOURCES: CoreBatteryStatsService ACTIVE - ${currentStatus.percentage}%, ${currentStatus.temperatureCelsius}°C, charging: ${currentStatus.isCharging}")
            } else {
                android.util.Log.e("HealthFragment", "DATA_SOURCES: CoreBatteryStatsService NOT ACTIVE!")
            }

            // Check HealthViewModel chart data
            val healthChartData = healthViewModel.uiState.value.chartData
            if (healthChartData != null) {
                android.util.Log.d("HealthFragment", "DATA_SOURCES: HealthViewModel chart data - Battery: ${healthChartData.batteryPercentageEntries.size}, Temperature: ${healthChartData.temperatureEntries.size}")
            } else {
                android.util.Log.w("HealthFragment", "DATA_SOURCES: HealthViewModel chart data is NULL")
            }

        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "DATA_SOURCES: Error analyzing data sources", e)
        }

        android.util.Log.d("HealthFragment", "DATA_SOURCES: === DATA SOURCE ANALYSIS COMPLETE ===")
    }

    /**
     * Tests chart data sources by forcing updates and comparing results.
     */
    private fun testChartDataSources() {
        android.util.Log.d("HealthFragment", "CHART_TEST: === TESTING CHART DATA SOURCES ===")

        lifecycleScope.launch {
            try {
                // Test 1: Force HealthViewModel chart update
                android.util.Log.d("HealthFragment", "CHART_TEST: Step 1 - Testing HealthViewModel chart update")
                healthViewModel.updateChartTimeRange(4)
                kotlinx.coroutines.delay(1000)

                val healthChartData = healthViewModel.uiState.value.chartData
                if (healthChartData != null) {
                    android.util.Log.d("HealthFragment", "CHART_TEST: HealthViewModel 4h data - Battery: ${healthChartData.batteryPercentageEntries.size}, Temperature: ${healthChartData.temperatureEntries.size}")
                } else {
                    android.util.Log.w("HealthFragment", "CHART_TEST: HealthViewModel returned NULL chart data")
                }

                // Test 2: Direct HistoryBatteryRepository access
                android.util.Log.d("HealthFragment", "CHART_TEST: Step 2 - Testing direct HistoryBatteryRepository access")
                val directBattery = historyBatteryRepository.getHistoryBatteryForHours(4)
                val directTemp = historyBatteryRepository.getHistoryTemperatureForHours(4)
                android.util.Log.d("HealthFragment", "CHART_TEST: Direct HistoryBatteryRepository 4h - Battery: ${directBattery.size}, Temperature: ${directTemp.size}")

                // Test 3: Compare with legacy BatteryViewModel
                android.util.Log.d("HealthFragment", "CHART_TEST: Step 3 - Testing legacy BatteryViewModel")
                val legacyBattery = batteryViewModel.getHistoryBatteryForHours(4)
                val legacyTemp = batteryViewModel.getHistoryTemperatureForHours(4)
                android.util.Log.d("HealthFragment", "CHART_TEST: Legacy BatteryViewModel 4h - Battery: ${legacyBattery.size}, Temperature: ${legacyTemp.size}")

                // Test 4: Force chart updates using new data source
                android.util.Log.d("HealthFragment", "CHART_TEST: Step 4 - Testing chart updates with new data source")
                updatePercentageChartOnly(4)
                updateTemperatureChartOnly(4)

                android.util.Log.d("HealthFragment", "CHART_TEST: === CHART DATA SOURCE TEST COMPLETE ===")

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "CHART_TEST: Error during chart data source test", e)
            }
        }
    }

    /**
     * Logs comprehensive chart state for debugging.
     */
    private fun logChartState(
        chart: com.github.mikephil.charting.charts.LineChart,
        type: HistoryType,
        entries: List<Entry>,
        dataSet: LineDataSet
    ) {
        try {
            android.util.Log.d("HealthFragment", "CHART_DEBUG: === ${type.name} Chart State Analysis ===")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart visibility: ${chart.visibility}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart width: ${chart.width}, height: ${chart.height}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart bounds: ${chart.left}, ${chart.top}, ${chart.right}, ${chart.bottom}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart has data: ${chart.data != null}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: DataSet entry count: ${dataSet.entryCount}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: DataSet visible: ${dataSet.isVisible}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: DataSet color: ${dataSet.color}")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: DataSet line width: ${dataSet.lineWidth}")

            if (entries.isNotEmpty()) {
                val firstEntry = entries.first()
                val lastEntry = entries.last()
                android.util.Log.d("HealthFragment", "CHART_DEBUG: First entry: x=${firstEntry.x}, y=${firstEntry.y}")
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Last entry: x=${lastEntry.x}, y=${lastEntry.y}")
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Y-axis range: ${entries.minOfOrNull { it.y }} to ${entries.maxOfOrNull { it.y }}")
            }

            // Check for any chart library errors
            val chartData = chart.data
            if (chartData != null) {
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart data set count: ${chartData.dataSetCount}")
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart data entry count: ${chartData.entryCount}")
            } else {
                android.util.Log.w("HealthFragment", "CHART_DEBUG: Chart data is null!")
            }

        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "CHART_DEBUG: Error logging chart state", e)
        }
    }

    /**
     * Logs chart state after invalidation.
     */
    private fun logChartStateAfterInvalidation(
        chart: com.github.mikephil.charting.charts.LineChart,
        type: HistoryType
    ) {
        try {
            android.util.Log.d("HealthFragment", "CHART_DEBUG: === ${type.name} Chart Post-Invalidation State ===")
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart renderer: ${chart.renderer != null}")

            // Check if chart has valid bounds for drawing
            val hasValidBounds = chart.width > 0 && chart.height > 0
            android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart has valid bounds: $hasValidBounds")

            if (!hasValidBounds) {
                android.util.Log.w("HealthFragment", "CHART_DEBUG: Chart has invalid bounds - this may prevent rendering!")
            }

            // Check if chart data is still present after invalidation
            val dataAfterInvalidation = chart.data
            if (dataAfterInvalidation != null) {
                android.util.Log.d("HealthFragment", "CHART_DEBUG: Chart data still present after invalidation: ${dataAfterInvalidation.entryCount} entries")
            } else {
                android.util.Log.w("HealthFragment", "CHART_DEBUG: Chart data is null after invalidation!")
            }

        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "CHART_DEBUG: Error logging post-invalidation state", e)
        }
    }

    /**
     * Simulates a new charging session to test if session count increases from 10 to 11.
     */
    private fun simulateNewChargingSession() {
        android.util.Log.d("HealthFragment", "DEBUG_MENU: === SIMULATING NEW CHARGING SESSION ===")

        lifecycleScope.launch {
            try {
                // Get current session count
                val currentSessions = chargingSessionManager.getTotalSessions()
                android.util.Log.d("HealthFragment", "DEBUG_MENU: Current session count: $currentSessions")

                // Simulate new session
                chargingSessionManager.simulateNewChargingSession()

                // Wait a moment for data to propagate
                kotlinx.coroutines.delay(500)

                // Check new session count
                val newSessions = chargingSessionManager.getTotalSessions()
                android.util.Log.d("HealthFragment", "DEBUG_MENU: New session count: $newSessions")

                // Refresh health data to trigger recalculation
                healthViewModel.refreshHealthData()

                android.util.Log.d("HealthFragment", "DEBUG_MENU: ✅ Session simulation completed: $currentSessions → $newSessions")

                android.widget.Toast.makeText(requireContext(),
                    "Session simulated: $currentSessions → $newSessions sessions",
                    android.widget.Toast.LENGTH_LONG).show()

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "DEBUG_MENU: Error simulating session", e)
                android.widget.Toast.makeText(requireContext(),
                    "Error simulating session: ${e.message}",
                    android.widget.Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * Clears all sessions to test real data tracking.
     */
    private fun clearAllSessionsForTesting() {
        android.util.Log.d("HealthFragment", "DEBUG_MENU: === CLEARING ALL SESSIONS FOR REAL DATA TESTING ===")

        lifecycleScope.launch {
            try {
                chargingSessionManager.clearSessions()
                chargingSessionManager.disableSampleGeneration()

                // Refresh health data
                healthViewModel.refreshHealthData()

                android.util.Log.d("HealthFragment", "DEBUG_MENU: ✅ All sessions cleared - ready for real session tracking")

                android.widget.Toast.makeText(requireContext(),
                    "Sessions cleared - ready for real charging data",
                    android.widget.Toast.LENGTH_LONG).show()

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "DEBUG_MENU: Error clearing sessions", e)
            }
        }
    }

    /**
     * Generates sample sessions for testing.
     */
    private fun generateSampleSessions() {
        android.util.Log.d("HealthFragment", "DEBUG_MENU: === GENERATING SAMPLE SESSIONS ===")

        lifecycleScope.launch {
            try {
                chargingSessionManager.enableSampleGeneration()
                chargingSessionManager.addSampleSessionsIfEmpty()

                // Refresh health data
                healthViewModel.refreshHealthData()

                android.util.Log.d("HealthFragment", "DEBUG_MENU: ✅ Sample sessions generated")

                android.widget.Toast.makeText(requireContext(),
                    "Sample sessions generated",
                    android.widget.Toast.LENGTH_SHORT).show()

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "DEBUG_MENU: Error generating sample sessions", e)
            }
        }
    }



    /**
     * Simulates battery percentage changes to test real-time data collection and UI updates.
     */
    private fun simulateBatteryPercentageChanges() {
        android.util.Log.d("HealthFragment", "DEBUG_MENU: === STARTING BATTERY PERCENTAGE SIMULATION ===")

        lifecycleScope.launch {
            try {
                // Try to get service instance directly
                val serviceInstance = com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.getInstance()

                if (serviceInstance != null) {
                    android.util.Log.d("HealthFragment", "DEBUG_MENU: ✅ Got CoreBatteryStatsService instance")
                    android.util.Log.d("HealthFragment", "DEBUG_MENU: Triggering battery percentage simulation...")

                    serviceInstance.simulateBatteryPercentageChanges()

                    android.widget.Toast.makeText(requireContext(),
                        "🔋 Battery simulation started - check logcat for progress",
                        android.widget.Toast.LENGTH_LONG).show()

                } else {
                    // Fallback: Send intent to service
                    android.util.Log.d("HealthFragment", "DEBUG_MENU: Service instance not available, using intent approach")

                    val serviceIntent = android.content.Intent(requireContext(), com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService::class.java)
                    serviceIntent.action = com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.ACTION_SIMULATE_BATTERY_CHANGES

                    requireContext().startService(serviceIntent)

                    android.util.Log.d("HealthFragment", "DEBUG_MENU: ✅ Simulation intent sent to service")
                    android.widget.Toast.makeText(requireContext(),
                        "🔋 Battery simulation triggered via intent - check logcat",
                        android.widget.Toast.LENGTH_LONG).show()
                }

            } catch (e: Exception) {
                android.util.Log.e("HealthFragment", "DEBUG_MENU: Error starting battery simulation", e)
                android.widget.Toast.makeText(requireContext(),
                    "Error starting simulation: ${e.message}",
                    android.widget.Toast.LENGTH_LONG).show()
            }
        }
    }

    /**
     * Sets up a temporary debug button for easy testing of session simulation.
     * This button will be visible in the UI for quick access to debug functions.
     */
    private fun setupTemporaryDebugButton() {
        android.util.Log.d("HealthFragment", "DEBUG_BUTTON: Setting up temporary debug button")

        // Create a temporary debug button
        val debugButton = android.widget.Button(requireContext()).apply {
            text = "🔧 DEBUG: Simulate Session"
            setBackgroundColor(android.graphics.Color.parseColor("#FF4444"))
            setTextColor(android.graphics.Color.WHITE)
            textSize = 12f
            setPadding(16, 8, 16, 8)

            setOnClickListener {
                android.util.Log.d("HealthFragment", "DEBUG_BUTTON: Temporary debug button clicked")
                simulateNewChargingSession()
            }
        }

        // Add the button to the layout (find a suitable container)
        try {
            // Try to add to the main container
            val mainContainer = binding.root as? android.view.ViewGroup
            if (mainContainer != null) {
                val layoutParams = android.widget.LinearLayout.LayoutParams(
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    setMargins(16, 16, 16, 16)
                }

                debugButton.layoutParams = layoutParams
                mainContainer.addView(debugButton)

                android.util.Log.d("HealthFragment", "DEBUG_BUTTON: ✅ Temporary debug button added to UI")
            } else {
                android.util.Log.w("HealthFragment", "DEBUG_BUTTON: Could not find suitable container for debug button")
            }
        } catch (e: Exception) {
            android.util.Log.e("HealthFragment", "DEBUG_BUTTON: Error adding debug button to UI", e)
        }
    }

    // endregion
}
