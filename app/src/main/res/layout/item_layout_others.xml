<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:id="@+id/contentContainer"
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:padding="16dp">


        <LinearLayout
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/itemTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Status Bar"
                android:textColor="?attr/black"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/itemDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/itemTitle"
                android:layout_marginTop="4dp"
                android:text="Customize your status bar with icons"
                android:textColor="?attr/black"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:layout_weight="1"
            android:id="@+id/itemIcon"
            android:layout_width="150dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:contentDescription="icon" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
