package com.tqhit.battery.one.features.stats.health.data

import org.junit.Assert.*
import org.junit.Test

/**
 * Unit tests for HealthStatus data model.
 * Tests validation, calculations, and factory methods.
 */
class HealthStatusTest {
    
    @Test
    fun `isValid returns true for valid health status`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 85,
            totalSessions = 50,
            designCapacityMah = 3000,
            effectiveCapacityMah = 2550,
            calculationMode = HealthCalculationMode.CUMULATIVE,
            timestampEpochMillis = System.currentTimeMillis()
        )
        
        // Act & Assert
        assertTrue(healthStatus.isValid())
    }
    
    @Test
    fun `isValid returns false for invalid health percentage`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 150, // Invalid: > 100
            totalSessions = 50,
            designCapacityMah = 3000,
            effectiveCapacityMah = 2550,
            calculationMode = HealthCalculationMode.CUMULATIVE,
            timestampEpochMillis = System.currentTimeMillis()
        )
        
        // Act & Assert
        assertFalse(healthStatus.isValid())
    }
    
    @Test
    fun `isValid returns false for negative sessions`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 85,
            totalSessions = -5, // Invalid: negative
            designCapacityMah = 3000,
            effectiveCapacityMah = 2550,
            calculationMode = HealthCalculationMode.CUMULATIVE,
            timestampEpochMillis = System.currentTimeMillis()
        )
        
        // Act & Assert
        assertFalse(healthStatus.isValid())
    }
    
    @Test
    fun `isValid returns false for zero design capacity`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 85,
            totalSessions = 50,
            designCapacityMah = 0, // Invalid: zero capacity
            effectiveCapacityMah = 2550,
            calculationMode = HealthCalculationMode.CUMULATIVE,
            timestampEpochMillis = System.currentTimeMillis()
        )
        
        // Act & Assert
        assertFalse(healthStatus.isValid())
    }
    
    @Test
    fun `isValid returns false for effective capacity greater than design capacity`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 85,
            totalSessions = 50,
            designCapacityMah = 3000,
            effectiveCapacityMah = 3500, // Invalid: > design capacity
            calculationMode = HealthCalculationMode.CUMULATIVE,
            timestampEpochMillis = System.currentTimeMillis()
        )
        
        // Act & Assert
        assertFalse(healthStatus.isValid())
    }
    
    @Test
    fun `getDegradationPercentage returns correct value`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 75,
            totalSessions = 50,
            designCapacityMah = 3000,
            effectiveCapacityMah = 2250,
            calculationMode = HealthCalculationMode.CUMULATIVE
        )
        
        // Act
        val degradation = healthStatus.getDegradationPercentage()
        
        // Assert
        assertEquals(25, degradation) // 100 - 75 = 25%
    }
    
    @Test
    fun `getCapacityLossMah returns correct value`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 80,
            totalSessions = 50,
            designCapacityMah = 4000,
            effectiveCapacityMah = 3200,
            calculationMode = HealthCalculationMode.CUMULATIVE
        )
        
        // Act
        val capacityLoss = healthStatus.getCapacityLossMah()
        
        // Assert
        assertEquals(800, capacityLoss) // 4000 - 3200 = 800 mAh
    }
    
    @Test
    fun `createDefault returns valid default health status`() {
        // Arrange
        val designCapacity = 3500
        
        // Act
        val defaultStatus = HealthStatus.createDefault(designCapacity)
        
        // Assert
        assertEquals(100, defaultStatus.healthPercentage)
        assertEquals(0, defaultStatus.totalSessions)
        assertEquals(designCapacity, defaultStatus.designCapacityMah)
        assertEquals(designCapacity, defaultStatus.effectiveCapacityMah)
        assertEquals(HealthCalculationMode.CUMULATIVE, defaultStatus.calculationMode)
        assertTrue(defaultStatus.isValid())
    }
    
    @Test
    fun `createCalculated with cumulative mode returns correct health status`() {
        // Arrange
        val totalSessions = 125
        val designCapacity = 3000
        val calculationMode = HealthCalculationMode.CUMULATIVE
        
        // Act
        val calculatedStatus = HealthStatus.createCalculated(
            totalSessions, designCapacity, calculationMode
        )
        
        // Assert
        // Formula: 100 - (125 / 500 * 80) = 100 - 20 = 80%
        assertEquals(80, calculatedStatus.healthPercentage)
        assertEquals(125, calculatedStatus.totalSessions)
        assertEquals(3000, calculatedStatus.designCapacityMah)
        assertEquals(2400, calculatedStatus.effectiveCapacityMah) // 3000 * 80 / 100
        assertEquals(HealthCalculationMode.CUMULATIVE, calculatedStatus.calculationMode)
        assertTrue(calculatedStatus.isValid())
    }
    
    @Test
    fun `createCalculated with singular mode returns zero health`() {
        // Arrange
        val totalSessions = 125
        val designCapacity = 3000
        val calculationMode = HealthCalculationMode.SINGULAR
        
        // Act
        val calculatedStatus = HealthStatus.createCalculated(
            totalSessions, designCapacity, calculationMode
        )
        
        // Assert
        assertEquals(0, calculatedStatus.healthPercentage) // Singular mode returns 0
        assertEquals(125, calculatedStatus.totalSessions)
        assertEquals(3000, calculatedStatus.designCapacityMah)
        assertEquals(0, calculatedStatus.effectiveCapacityMah) // 3000 * 0 / 100
        assertEquals(HealthCalculationMode.SINGULAR, calculatedStatus.calculationMode)
        assertTrue(calculatedStatus.isValid())
    }
    
    @Test
    fun `createCalculated with high sessions clamps health to zero`() {
        // Arrange
        val totalSessions = 1000 // Very high number
        val designCapacity = 3000
        val calculationMode = HealthCalculationMode.CUMULATIVE
        
        // Act
        val calculatedStatus = HealthStatus.createCalculated(
            totalSessions, designCapacity, calculationMode
        )
        
        // Assert
        assertEquals(0, calculatedStatus.healthPercentage) // Should be clamped to 0
        assertEquals(1000, calculatedStatus.totalSessions)
        assertEquals(3000, calculatedStatus.designCapacityMah)
        assertEquals(0, calculatedStatus.effectiveCapacityMah)
        assertEquals(HealthCalculationMode.CUMULATIVE, calculatedStatus.calculationMode)
        assertTrue(calculatedStatus.isValid())
    }
    
    @Test
    fun `health calculation mode enum has correct display names`() {
        // Act & Assert
        assertEquals("Cumulative", HealthCalculationMode.CUMULATIVE.getDisplayName())
        assertEquals("Singular", HealthCalculationMode.SINGULAR.getDisplayName())
    }
    
    @Test
    fun `health calculation mode enum has correct descriptions`() {
        // Act & Assert
        assertEquals(
            "Uses all charging sessions to calculate battery health",
            HealthCalculationMode.CUMULATIVE.getDescription()
        )
        assertEquals(
            "Requires full charge cycles (15% to 100%) for accurate calculation",
            HealthCalculationMode.SINGULAR.getDescription()
        )
    }
    
    @Test
    fun `timestamp is set correctly on creation`() {
        // Arrange
        val beforeCreation = System.currentTimeMillis()
        
        // Act
        val healthStatus = HealthStatus(
            healthPercentage = 85,
            totalSessions = 50,
            designCapacityMah = 3000,
            effectiveCapacityMah = 2550,
            calculationMode = HealthCalculationMode.CUMULATIVE
        )
        
        val afterCreation = System.currentTimeMillis()
        
        // Assert
        assertTrue("Timestamp should be between before and after creation",
            healthStatus.timestampEpochMillis >= beforeCreation &&
            healthStatus.timestampEpochMillis <= afterCreation)
    }
    
    @Test
    fun `edge case zero health percentage is valid`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 0,
            totalSessions = 1000,
            designCapacityMah = 3000,
            effectiveCapacityMah = 0,
            calculationMode = HealthCalculationMode.CUMULATIVE
        )
        
        // Act & Assert
        assertTrue(healthStatus.isValid())
        assertEquals(100, healthStatus.getDegradationPercentage())
        assertEquals(3000, healthStatus.getCapacityLossMah())
    }
    
    @Test
    fun `edge case perfect health is valid`() {
        // Arrange
        val healthStatus = HealthStatus(
            healthPercentage = 100,
            totalSessions = 0,
            designCapacityMah = 3000,
            effectiveCapacityMah = 3000,
            calculationMode = HealthCalculationMode.CUMULATIVE
        )
        
        // Act & Assert
        assertTrue(healthStatus.isValid())
        assertEquals(0, healthStatus.getDegradationPercentage())
        assertEquals(0, healthStatus.getCapacityLossMah())
    }
}
