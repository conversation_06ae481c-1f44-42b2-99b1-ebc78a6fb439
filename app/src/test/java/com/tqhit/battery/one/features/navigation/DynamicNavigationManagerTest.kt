package com.tqhit.battery.one.features.navigation

import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

@ExperimentalCoroutinesApi
class DynamicNavigationManagerTest {

    private lateinit var dynamicNavigationManager: DynamicNavigationManager
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider
    private lateinit var mockFragmentManager: FragmentManager
    private lateinit var mockBottomNavigationView: BottomNavigationView
    private lateinit var mockLifecycleOwner: LifecycleOwner
    private lateinit var mockFragmentTransaction: FragmentTransaction
    
    private val testScope = TestScope()
    private val batteryStatusFlow = MutableStateFlow<CoreBatteryStatus?>(null)
    
    @Before
    fun setUp() {
        mockCoreBatteryStatsProvider = mockk()
        mockFragmentManager = mockk(relaxed = true)
        mockBottomNavigationView = mockk(relaxed = true)
        mockLifecycleOwner = mockk()
        mockFragmentTransaction = mockk(relaxed = true)
        
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns batteryStatusFlow
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns null
        every { mockFragmentManager.beginTransaction() } returns mockFragmentTransaction
        every { mockLifecycleOwner.lifecycleScope } returns testScope
        
        dynamicNavigationManager = DynamicNavigationManager(mockCoreBatteryStatsProvider)
    }

    @Test
    fun `initialize sets up navigation manager correctly`() = runTest {
        // Arrange
        val fragmentContainerId = R.id.nav_host_fragment
        
        // Act
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            fragmentContainerId,
            mockLifecycleOwner
        )
        
        // Assert
        assertTrue(dynamicNavigationManager.isInitialized())
    }

    @Test
    fun `initial state is charging when battery is charging`() = runTest {
        // Arrange
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns chargingStatus
        
        // Act
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )
        
        // Assert
        val currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertTrue(currentState!!.isCharging)
        assertEquals(R.id.chargeFragment, currentState.activeFragmentId)
    }

    @Test
    fun `initial state is discharging when battery is not charging`() = runTest {
        // Arrange
        val dischargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = false)
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns dischargingStatus
        
        // Act
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )
        
        // Assert
        val currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertFalse(currentState!!.isCharging)
        assertEquals(R.id.dischargeFragment, currentState.activeFragmentId)
    }

    @Test
    fun `charging state change updates navigation state`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )
        
        // Act - simulate charging started
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        batteryStatusFlow.value = chargingStatus
        
        // Assert
        val currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertTrue(currentState!!.isCharging)
        assertEquals(R.id.chargeFragment, currentState.activeFragmentId)
    }

    @Test
    fun `discharging state change updates navigation state`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )

        // Act - simulate charging stopped
        val dischargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = false)
        batteryStatusFlow.value = dischargingStatus

        // Assert
        val currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertFalse(currentState!!.isCharging)
        assertEquals(R.id.dischargeFragment, currentState.activeFragmentId)
    }

    @Test
    fun `dynamic switching from charge to discharge fragment when charging stops`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )

        // Navigate to charge fragment first
        dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)

        // Verify initial state
        var currentState = dynamicNavigationManager.getCurrentState()
        assertEquals(R.id.chargeFragment, currentState?.activeFragmentId)

        // Act - simulate charging stopped while on charge fragment
        val dischargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = false)
        batteryStatusFlow.value = dischargingStatus

        // Assert - should automatically switch to discharge fragment
        currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertFalse(currentState!!.isCharging)
        assertEquals(R.id.dischargeFragment, currentState.activeFragmentId)
    }

    @Test
    fun `dynamic switching from discharge to charge fragment when charging starts`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )

        // Navigate to discharge fragment first
        dynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment)

        // Verify initial state
        var currentState = dynamicNavigationManager.getCurrentState()
        assertEquals(R.id.dischargeFragment, currentState?.activeFragmentId)

        // Act - simulate charging started while on discharge fragment
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        batteryStatusFlow.value = chargingStatus

        // Assert - should automatically switch to charge fragment
        currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertTrue(currentState!!.isCharging)
        assertEquals(R.id.chargeFragment, currentState.activeFragmentId)
    }

    @Test
    fun `no dynamic switching when user is on animation fragment`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )

        // Navigate to animation fragment
        dynamicNavigationManager.handleUserNavigation(R.id.animationGridFragment)

        // Verify initial state
        var currentState = dynamicNavigationManager.getCurrentState()
        assertEquals(R.id.animationGridFragment, currentState?.activeFragmentId)

        // Act - simulate charging state change while on animation fragment
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        batteryStatusFlow.value = chargingStatus

        // Assert - should stay on animation fragment, only update charging state
        currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertTrue(currentState!!.isCharging)
        assertEquals(R.id.animationGridFragment, currentState.activeFragmentId) // Should not switch
    }

    @Test
    fun `no dynamic switching when user is on others fragment`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )

        // Navigate to others fragment
        dynamicNavigationManager.handleUserNavigation(R.id.othersFragment)

        // Verify initial state
        var currentState = dynamicNavigationManager.getCurrentState()
        assertEquals(R.id.othersFragment, currentState?.activeFragmentId)

        // Act - simulate charging state change while on others fragment
        val dischargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = false)
        batteryStatusFlow.value = dischargingStatus

        // Assert - should stay on others fragment, only update charging state
        currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        assertFalse(currentState!!.isCharging)
        assertEquals(R.id.othersFragment, currentState.activeFragmentId) // Should not switch
    }

    @Test
    fun `user navigation to visible item succeeds`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )
        
        // Set initial charging state
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        batteryStatusFlow.value = chargingStatus
        
        // Act - navigate to health fragment (always visible)
        val result = dynamicNavigationManager.handleUserNavigation(R.id.healthFragment)
        
        // Assert
        assertTrue(result)
        val currentState = dynamicNavigationManager.getCurrentState()
        assertEquals(R.id.healthFragment, currentState?.activeFragmentId)
    }

    @Test
    fun `user navigation to hidden item fails`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )
        
        // Set charging state (discharge fragment should be hidden)
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        batteryStatusFlow.value = chargingStatus
        
        // Act - try to navigate to discharge fragment (should be hidden)
        val result = dynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment)
        
        // Assert
        assertFalse(result)
        val currentState = dynamicNavigationManager.getCurrentState()
        assertEquals(R.id.chargeFragment, currentState?.activeFragmentId) // Should remain on charge fragment
    }

    @Test
    fun `navigation state includes correct visible menu items for charging`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )
        
        // Act
        val chargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = true)
        batteryStatusFlow.value = chargingStatus
        
        // Assert
        val currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        
        val expectedVisibleItems = listOf(
            R.id.chargeFragment,
            R.id.healthFragment,
            R.id.settingsFragment,
            R.id.animationGridFragment
        )
        
        expectedVisibleItems.forEach { itemId ->
            assertTrue("Item $itemId should be visible", currentState!!.isMenuItemVisible(itemId))
        }
        
        assertFalse("Discharge fragment should be hidden", currentState!!.isMenuItemVisible(R.id.dischargeFragment))
    }

    @Test
    fun `navigation state includes correct visible menu items for discharging`() = runTest {
        // Arrange
        dynamicNavigationManager.initialize(
            mockFragmentManager,
            mockBottomNavigationView,
            R.id.nav_host_fragment,
            mockLifecycleOwner
        )
        
        // Act
        val dischargingStatus = CoreBatteryStatus.createDefault().copy(isCharging = false)
        batteryStatusFlow.value = dischargingStatus
        
        // Assert
        val currentState = dynamicNavigationManager.getCurrentState()
        assertNotNull(currentState)
        
        val expectedVisibleItems = listOf(
            R.id.dischargeFragment,
            R.id.healthFragment,
            R.id.settingsFragment,
            R.id.animationGridFragment
        )
        
        expectedVisibleItems.forEach { itemId ->
            assertTrue("Item $itemId should be visible", currentState!!.isMenuItemVisible(itemId))
        }
        
        assertFalse("Charge fragment should be hidden", currentState!!.isMenuItemVisible(R.id.chargeFragment))
    }

    @Test
    fun `uninitialized manager returns false for user navigation`() = runTest {
        // Act
        val result = dynamicNavigationManager.handleUserNavigation(R.id.healthFragment)
        
        // Assert
        assertFalse(result)
        assertFalse(dynamicNavigationManager.isInitialized())
    }
}
