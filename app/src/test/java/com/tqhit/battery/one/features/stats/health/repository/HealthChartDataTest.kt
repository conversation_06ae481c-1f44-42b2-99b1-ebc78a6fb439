package com.tqhit.battery.one.features.stats.health.repository

import com.github.mikephil.charting.data.Entry
import com.tqhit.battery.one.features.stats.health.data.HealthChartData
import org.junit.Assert.*
import org.junit.Test

/**
 * Unit tests for HealthChartData model and chart functionality.
 * Tests data validation, chart entry conversion, and sample data generation.
 */
class HealthChartDataTest {

    @Test
    fun `createSample generates valid chart data for all time ranges`() {
        val timeRanges = listOf(4, 8, 12, 24)
        
        timeRanges.forEach { timeRange ->
            val chartData = HealthChartData.createSample(timeRange)
            
            // Verify basic structure
            assertTrue("Chart data should be valid for ${timeRange}h", chartData.isValid())
            assertEquals("Time range should match", timeRange, chartData.selectedTimeRangeHours)
            assertEquals("Should have 7 days of wear data", 7, chartData.dailyWearData.size)
            
            // Verify data points are reasonable for time range
            val expectedDataPoints = when (timeRange) {
                4 -> 24
                8 -> 32
                12 -> 36
                24 -> 48
                else -> 24
            }
            
            assertTrue("Battery entries should have reasonable count for ${timeRange}h", 
                chartData.batteryPercentageEntries.size >= expectedDataPoints - 5)
            assertTrue("Temperature entries should have reasonable count for ${timeRange}h", 
                chartData.temperatureEntries.size >= expectedDataPoints - 5)
        }
    }

    @Test
    fun `battery percentage entries have valid ranges`() {
        val chartData = HealthChartData.createSample(4)
        
        chartData.batteryPercentageEntries.forEach { entry ->
            assertTrue("Battery percentage should be between 0-100%", 
                entry.y in 0f..100f)
            assertTrue("X value should be non-negative", entry.x >= 0f)
        }
    }

    @Test
    fun `temperature entries have realistic ranges`() {
        val chartData = HealthChartData.createSample(8)
        
        chartData.temperatureEntries.forEach { entry ->
            assertTrue("Temperature should be realistic (15-45°C)", 
                entry.y in 15f..45f)
            assertTrue("X value should be non-negative", entry.x >= 0f)
        }
    }

    @Test
    fun `daily wear data has reasonable values`() {
        val chartData = HealthChartData.createSample(12)
        
        assertEquals("Should have exactly 7 days", 7, chartData.dailyWearData.size)
        
        chartData.dailyWearData.forEach { wear ->
            assertTrue("Daily wear should be positive", wear >= 0.0)
            assertTrue("Daily wear should be reasonable (< 5.0)", wear < 5.0)
        }
    }

    @Test
    fun `createEmpty generates valid empty chart data`() {
        val emptyData = HealthChartData.createEmpty(4)
        
        assertFalse("Empty data should not be valid for charts", emptyData.isValid())
        assertEquals("Time range should be set", 4, emptyData.selectedTimeRangeHours)
        assertTrue("Battery entries should be empty", emptyData.batteryPercentageEntries.isEmpty())
        assertTrue("Temperature entries should be empty", emptyData.temperatureEntries.isEmpty())
        assertEquals("Should have 7 days of zero wear", 7, emptyData.dailyWearData.size)
        assertTrue("All wear data should be zero", emptyData.dailyWearData.all { it == 0.0 })
    }

    @Test
    fun `chart data validation works correctly`() {
        // Valid chart data
        val validData = HealthChartData(
            batteryPercentageEntries = listOf(Entry(0f, 50f), Entry(1f, 45f)),
            temperatureEntries = listOf(Entry(0f, 25f), Entry(1f, 27f)),
            dailyWearData = List(7) { 1.0 },
            selectedTimeRangeHours = 4
        )
        assertTrue("Valid data should pass validation", validData.isValid())

        // Invalid time range
        val invalidTimeRange = validData.copy(selectedTimeRangeHours = 3)
        assertFalse("Invalid time range should fail validation", invalidTimeRange.isValid())

        // Wrong number of daily wear days
        val invalidWearDays = validData.copy(dailyWearData = List(5) { 1.0 })
        assertFalse("Wrong wear days count should fail validation", invalidWearDays.isValid())

        // Empty entries
        val emptyEntries = validData.copy(batteryPercentageEntries = emptyList())
        assertFalse("Empty battery entries should fail validation", emptyEntries.isValid())
    }

    @Test
    fun `getMaxBatteryPercentage returns correct values`() {
        val entries = listOf(
            Entry(0f, 30f),
            Entry(1f, 85f),
            Entry(2f, 42f)
        )
        
        val chartData = HealthChartData(
            batteryPercentageEntries = entries,
            temperatureEntries = listOf(Entry(0f, 25f)),
            dailyWearData = List(7) { 1.0 },
            selectedTimeRangeHours = 4
        )
        
        assertEquals("Should return maximum battery percentage", 85f, chartData.getMaxBatteryPercentage(), 0.01f)
    }

    @Test
    fun `getMaxBatteryPercentage handles empty data`() {
        val emptyData = HealthChartData.createEmpty(4)
        assertEquals("Empty data should return 0", 0f, emptyData.getMaxBatteryPercentage(), 0.01f)
    }

    @Test
    fun `chart data entries are properly ordered`() {
        val chartData = HealthChartData.createSample(4)
        
        // Check battery entries are ordered by X value
        val batteryXValues = chartData.batteryPercentageEntries.map { it.x }
        assertEquals("Battery X values should be ordered", batteryXValues.sorted(), batteryXValues)
        
        // Check temperature entries are ordered by X value
        val tempXValues = chartData.temperatureEntries.map { it.x }
        assertEquals("Temperature X values should be ordered", tempXValues.sorted(), tempXValues)
    }

    @Test
    fun `sample data shows realistic battery discharge pattern`() {
        val chartData = HealthChartData.createSample(24) // 24-hour range
        
        val batteryValues = chartData.batteryPercentageEntries.map { it.y }
        val firstValue = batteryValues.first()
        val lastValue = batteryValues.last()
        
        // Battery should generally decrease over time (with some variation)
        assertTrue("Battery should start higher than it ends", firstValue > lastValue)
        assertTrue("Battery should start at reasonable level", firstValue > 70f)
        assertTrue("Battery should not discharge completely", lastValue > 15f)
    }

    @Test
    fun `temperature correlates with battery usage patterns`() {
        val chartData = HealthChartData.createSample(12)
        
        val temperatureValues = chartData.temperatureEntries.map { it.y }
        val avgTemperature = temperatureValues.average()
        
        // Temperature should be in realistic range
        assertTrue("Average temperature should be reasonable", avgTemperature in 20.0..35.0)
        
        // Should have some variation (not all the same)
        val tempVariation = temperatureValues.maxOrNull()!! - temperatureValues.minOrNull()!!
        assertTrue("Temperature should have some variation", tempVariation > 2f)
    }
}
