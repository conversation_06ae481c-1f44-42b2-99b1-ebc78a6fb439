package com.tqhit.battery.one.fragment.main.others

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.testing.FragmentScenario
import androidx.fragment.app.testing.launchFragmentInContainer
import androidx.lifecycle.Lifecycle
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.domain.model.CoreBatteryStatus
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * Unit tests for OthersFragment.
 * Tests battery state changes, navigation logic, and UI updates.
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
@Config(sdk = [28])
class OthersFragmentTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @MockK
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider

    @MockK
    private lateinit var mockDynamicNavigationManager: DynamicNavigationManager

    @MockK
    private lateinit var mockAppViewModel: AppViewModel

    private lateinit var batteryStatusFlow: MutableStateFlow<CoreBatteryStatus?>
    private lateinit var scenario: FragmentScenario<OthersFragment>

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        hiltRule.inject()

        // Setup battery status flow
        batteryStatusFlow = MutableStateFlow(null)
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns batteryStatusFlow

        // Setup default mock behaviors
        every { mockDynamicNavigationManager.isInitialized() } returns true
        every { mockDynamicNavigationManager.handleUserNavigation(any()) } returns true
        every { mockAppViewModel.isAntiThiefEnabled() } returns false
        every { mockAppViewModel.isAntiThiefPasswordSet() } returns false
        every { mockAppViewModel.setAntiThiefEnabled(any()) } returns Unit
        every { mockAppViewModel.setAntiThiefPassword(any()) } returns Unit
    }

    @Test
    fun `battery state change updates UI correctly when charging`() = runTest {
        // Given
        val chargingStatus = mockk<CoreBatteryStatus> {
            every { isCharging } returns true
        }
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns chargingStatus

        // When
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // Simulate battery state change
        batteryStatusFlow.value = chargingStatus

        // Then
        scenario.onFragment { fragment ->
            // Verify that the fragment processes the charging state correctly
            // This would typically involve checking UI state, but since we're testing
            // the logic, we verify the internal state handling
            assert(fragment.isDeviceCharging == true)
        }
    }

    @Test
    fun `battery state change updates UI correctly when not charging`() = runTest {
        // Given
        val dischargingStatus = mockk<CoreBatteryStatus> {
            every { isCharging } returns false
        }
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns dischargingStatus

        // When
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // Simulate battery state change
        batteryStatusFlow.value = dischargingStatus

        // Then
        scenario.onFragment { fragment ->
            assert(fragment.isDeviceCharging == false)
        }
    }

    @Test
    fun `navigation to charge fragment uses DynamicNavigationManager when charging`() = runTest {
        // Given
        val chargingStatus = mockk<CoreBatteryStatus> {
            every { isCharging } returns true
        }
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns chargingStatus

        // When
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        scenario.onFragment { fragment ->
            // Simulate clicking on charge item when device is charging
            fragment.handleItemClick("ChargeFragment")
        }

        // Then
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment) }
    }

    @Test
    fun `navigation to charge fragment uses DynamicNavigationManager when not charging`() = runTest {
        // Given
        val dischargingStatus = mockk<CoreBatteryStatus> {
            every { isCharging } returns false
        }
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns dischargingStatus

        // When
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        scenario.onFragment { fragment ->
            // Simulate clicking on charge item when device is not charging
            fragment.handleItemClick("ChargeFragment")
        }

        // Then
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) }
    }

    @Test
    fun `navigation to health fragment uses DynamicNavigationManager`() = runTest {
        // Given
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // When
        scenario.onFragment { fragment ->
            fragment.handleItemClick("healthFragment")
        }

        // Then
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.healthFragment) }
    }

    @Test
    fun `navigation to settings fragment uses DynamicNavigationManager`() = runTest {
        // Given
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // When
        scenario.onFragment { fragment ->
            fragment.handleItemClick("settingsFragment")
        }

        // Then
        verify { mockDynamicNavigationManager.handleUserNavigation(R.id.settingsFragment) }
    }

    @Test
    fun `anti-thief switch enables feature when password is set`() = runTest {
        // Given
        every { mockAppViewModel.isAntiThiefPasswordSet() } returns true
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // When
        scenario.onFragment { fragment ->
            fragment.handleAntiThiefSwitchChange(true)
        }

        // Then
        verify { mockAppViewModel.setAntiThiefEnabled(true) }
    }

    @Test
    fun `anti-thief switch disables feature and clears password`() = runTest {
        // Given
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // When
        scenario.onFragment { fragment ->
            fragment.handleAntiThiefSwitchChange(false)
        }

        // Then
        verify { mockAppViewModel.setAntiThiefPassword("") }
        verify { mockAppViewModel.setAntiThiefEnabled(false) }
    }

    @Test
    fun `fragment handles CoreBatteryStatsProvider errors gracefully`() = runTest {
        // Given
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } throws RuntimeException("Test error")

        // When
        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // Then - Fragment should not crash and should use default state
        scenario.onFragment { fragment ->
            // Fragment should handle the error and set default charging state
            assert(fragment.isDeviceCharging == false) // Default state
        }
    }

    @Test
    fun `fragment falls back to direct navigation when DynamicNavigationManager fails`() = runTest {
        // Given
        every { mockDynamicNavigationManager.handleUserNavigation(any()) } returns false

        scenario = launchFragmentInContainer<OthersFragment>()
        scenario.moveToState(Lifecycle.State.STARTED)

        // When
        scenario.onFragment { fragment ->
            fragment.handleItemClick("ChargeFragment")
        }

        // Then - Should attempt DynamicNavigationManager first, then fall back
        verify { mockDynamicNavigationManager.handleUserNavigation(any()) }
        // Fallback behavior would be tested in integration tests
    }
}
